import { Routes, Route } from 'react-router-dom'
import { Layout } from '@/components/Layout'
import { HomePage } from '@/pages/HomePage'
import { DocumentsPage } from '@/pages/DocumentsPage'
import { SignaturesPage } from '@/pages/SignaturesPage'
import { DepartmentsPage } from '@/pages/DepartmentsPage'

function App() {
  return (
    <Layout>
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/documents" element={<DocumentsPage />} />
        <Route path="/signatures" element={<SignaturesPage />} />
        <Route path="/departments" element={<DepartmentsPage />} />
      </Routes>
    </Layout>
  )
}

export default App
