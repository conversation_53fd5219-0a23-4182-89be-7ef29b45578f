import { FileText, PenTool, Building2, Upload } from 'lucide-react'

export function HomePage() {
  const features = [
    {
      name: 'Gestión de Documentos',
      description: 'Carga y administra documentos PDF de forma segura',
      icon: FileText,
      color: 'bg-blue-500',
    },
    {
      name: 'Firmas Digitales',
      description: 'Crea y gestiona firmas digitales por departamento',
      icon: PenTool,
      color: 'bg-green-500',
    },
    {
      name: 'Organización',
      description: 'Organiza usuarios y firmas por departamentos',
      icon: Building2,
      color: 'bg-purple-500',
    },
    {
      name: 'Procesamiento Automático',
      description: 'Aplicación automática de firmas en documentos',
      icon: Upload,
      color: 'bg-orange-500',
    },
  ]

  return (
    <div className="px-4 py-6 sm:px-0">
      {/* Hero section */}
      <div className="text-center">
        <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
          Sistema de Firmas PDF
        </h1>
        <p className="mt-6 text-lg leading-8 text-gray-600">
          Gestiona y aplica firmas digitales en documentos PDF de forma automática y eficiente.
          Organiza por departamentos y optimiza tu flujo de trabajo.
        </p>
        <div className="mt-10 flex items-center justify-center gap-x-6">
          <a
            href="/documents"
            className="rounded-md bg-primary-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-primary-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-600"
          >
            Comenzar
          </a>
          <a href="/docs" className="text-sm font-semibold leading-6 text-gray-900">
            Ver documentación <span aria-hidden="true">→</span>
          </a>
        </div>
      </div>

      {/* Features section */}
      <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
        <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-2">
          {features.map((feature) => (
            <div key={feature.name} className="flex flex-col">
              <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                <div className={`h-10 w-10 flex items-center justify-center rounded-lg ${feature.color}`}>
                  <feature.icon className="h-6 w-6 text-white" aria-hidden="true" />
                </div>
                {feature.name}
              </dt>
              <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                <p className="flex-auto">{feature.description}</p>
              </dd>
            </div>
          ))}
        </dl>
      </div>

      {/* Stats section */}
      <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-7xl">
        <div className="grid grid-cols-1 gap-x-8 gap-y-16 text-center lg:grid-cols-3">
          <div className="mx-auto flex max-w-xs flex-col gap-y-4">
            <dt className="text-base leading-7 text-gray-600">Documentos procesados</dt>
            <dd className="order-first text-3xl font-semibold tracking-tight text-gray-900 sm:text-5xl">
              0
            </dd>
          </div>
          <div className="mx-auto flex max-w-xs flex-col gap-y-4">
            <dt className="text-base leading-7 text-gray-600">Firmas aplicadas</dt>
            <dd className="order-first text-3xl font-semibold tracking-tight text-gray-900 sm:text-5xl">
              0
            </dd>
          </div>
          <div className="mx-auto flex max-w-xs flex-col gap-y-4">
            <dt className="text-base leading-7 text-gray-600">Departamentos activos</dt>
            <dd className="order-first text-3xl font-semibold tracking-tight text-gray-900 sm:text-5xl">
              0
            </dd>
          </div>
        </div>
      </div>
    </div>
  )
}
