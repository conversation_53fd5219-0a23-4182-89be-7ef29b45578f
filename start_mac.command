#!/bin/bash

# Script de inicio para macOS
clear

echo "==============================================="
echo "   SISTEMA DE FIRMADO PDF - macOS"
echo "==============================================="
echo

# Obtener directorio del script
DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
cd "$DIR"

# Verificar Python
if ! command -v python3 &> /dev/null; then
    echo "❌ ERROR: Python 3 no está instalado"
    echo
    echo "Instala Python desde: https://www.python.org/downloads/"
    echo "O usa Homebrew: brew install python"
    echo
    read -p "Presiona Enter para salir..."
    exit 1
fi

echo "✅ Python encontrado: $(python3 --version)"

# Verificar archivos
if [ ! -f "app_gui.py" ]; then
    echo "❌ ERROR: No se encuentra app_gui.py"
    echo "Asegúrate de estar en el directorio correcto"
    read -p "Presiona Enter para salir..."
    exit 1
fi

echo
echo "🚀 Iniciando aplicación..."
echo

# Ejecutar aplicación
python3 app_gui.py

# Verificar código de salida
if [ $? -ne 0 ]; then
    echo
    echo "❌ ERROR: La aplicación terminó con errores"
    echo
    echo "💡 Soluciones posibles:"
    echo "1. Ejecutar: python3 install.py"
    echo "2. Instalar dependencias: pip3 install -r requirements.txt"
    echo "3. Verificar permisos de archivos"
    echo
    read -p "Presiona Enter para continuar..."
fi

echo
echo "👋 Aplicación cerrada"
read -p "Presiona Enter para salir..."
