#!/bin/bash

# Script de inicio inteligente para macOS
clear

echo "==============================================="
echo "   SISTEMA DE FIRMADO PDF - macOS"
echo "==============================================="
echo

# Obtener directorio del script
DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
cd "$DIR"

# Función para probar tkinter
test_tkinter() {
    local python_cmd="$1"
    "$python_cmd" -c "import tkinter; tkinter.Tk().withdraw()" 2>/dev/null
    return $?
}

# Función para encontrar Python funcional
find_working_python() {
    local python_candidates=(
        "python3"
        "/Library/Frameworks/Python.framework/Versions/3.13/bin/python3"
        "/Library/Frameworks/Python.framework/Versions/3.12/bin/python3"
        "/Library/Frameworks/Python.framework/Versions/3.11/bin/python3"
        "/opt/homebrew/bin/python3"
        "/usr/local/bin/python3"
        "/usr/bin/python3"
    )

    for python_cmd in "${python_candidates[@]}"; do
        if command -v "$python_cmd" &> /dev/null; then
            if test_tkinter "$python_cmd"; then
                echo "$python_cmd"
                return 0
            fi
        fi
    done

    return 1
}

# Verificar Python básico
if ! command -v python3 &> /dev/null; then
    echo "❌ ERROR: Python 3 no está instalado"
    echo
    echo "💡 Soluciones:"
    echo "1. Instalar desde: https://www.python.org/downloads/"
    echo "2. O usar Homebrew: brew install python"
    echo "3. O ejecutar: python3 install_macos.py"
    echo
    read -p "Presiona Enter para salir..."
    exit 1
fi

echo "✅ Python encontrado: $(python3 --version)"

# Verificar archivos necesarios
if [ ! -f "app_gui.py" ]; then
    echo "❌ ERROR: No se encuentra app_gui.py"
    echo "Asegúrate de estar en el directorio correcto"
    read -p "Presiona Enter para salir..."
    exit 1
fi

# Probar tkinter con Python actual
echo "🧪 Probando tkinter..."
if test_tkinter "python3"; then
    echo "✅ tkinter funciona correctamente"
    PYTHON_CMD="python3"
else
    echo "❌ tkinter no funciona con Python actual"
    echo "� Buscando Python alternativo..."

    PYTHON_CMD=$(find_working_python)

    if [ $? -eq 0 ] && [ -n "$PYTHON_CMD" ]; then
        echo "✅ Python funcional encontrado: $PYTHON_CMD"
    else
        echo "❌ No se encontró Python con tkinter funcional"
        echo
        echo "🔧 SOLUCIONES AUTOMÁTICAS:"
        echo "1. Ejecutar reparación automática:"
        echo "   python3 install_macos.py"
        echo
        echo "2. Diagnóstico detallado:"
        echo "   python3 diagnose_macos.py"
        echo
        echo "3. Instalación manual:"
        echo "   • Python oficial: https://www.python.org/downloads/"
        echo "   • Homebrew: brew install python-tk"
        echo

        read -p "¿Ejecutar reparación automática? (s/n): " choice
        if [[ "$choice" =~ ^[Ss] ]]; then
            echo "🔧 Ejecutando reparación..."
            python3 install_macos.py

            # Probar nuevamente
            PYTHON_CMD=$(find_working_python)
            if [ $? -eq 0 ] && [ -n "$PYTHON_CMD" ]; then
                echo "✅ Reparación exitosa"
            else
                echo "❌ Reparación falló"
                read -p "Presiona Enter para salir..."
                exit 1
            fi
        else
            read -p "Presiona Enter para salir..."
            exit 1
        fi
    fi
fi

echo
echo "🚀 Iniciando aplicación con $PYTHON_CMD..."
echo

# Ejecutar aplicación
"$PYTHON_CMD" app_gui.py

# Verificar código de salida
exit_code=$?
if [ $exit_code -ne 0 ]; then
    echo
    echo "❌ ERROR: La aplicación terminó con errores (código: $exit_code)"
    echo
    echo "💡 Soluciones posibles:"
    echo "1. Reparación automática: python3 install_macos.py"
    echo "2. Diagnóstico: python3 diagnose_macos.py"
    echo "3. Instalar dependencias: pip3 install -r requirements.txt"
    echo "4. Verificar permisos de archivos"
    echo
    read -p "Presiona Enter para continuar..."
fi

echo
echo "👋 Aplicación cerrada"
read -p "Presiona Enter para salir..."
