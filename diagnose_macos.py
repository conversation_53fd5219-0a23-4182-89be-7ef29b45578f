#!/usr/bin/env python3
"""
🔍 Diagnóstico de macOS - Sistema de Firmado PDF
Detecta y diagnostica problemas comunes de Python/tkinter en macOS
"""

import sys
import subprocess
import platform
import os
from pathlib import Path

def print_header():
    """Mostrar header del diagnóstico"""
    print("=" * 60)
    print("🔍 DIAGNÓSTICO DE macOS - SISTEMA DE FIRMADO PDF")
    print("=" * 60)

def check_system_info():
    """Verificar información del sistema"""
    print("🖥️ Información del Sistema:")
    print(f"   Sistema: {platform.system()} {platform.release()}")
    print(f"   Arquitectura: {platform.machine()}")
    print(f"   Versión macOS: {platform.mac_ver()[0]}")
    print()

def check_python_installation():
    """Verificar instalación de Python"""
    print("🐍 Información de Python:")
    print(f"   Versión: {sys.version}")
    print(f"   Ejecutable: {sys.executable}")
    print(f"   Ruta: {sys.path[0]}")
    
    # Detectar método de instalación
    if "Homebrew" in sys.version or "/opt/homebrew" in sys.executable or "/usr/local" in sys.executable:
        print("   📦 Instalación: Homebrew")
        return "homebrew"
    elif "/System/Library" in sys.executable:
        print("   🍎 Instalación: Sistema (macOS)")
        return "system"
    elif "python.org" in sys.executable or "/Library/Frameworks" in sys.executable:
        print("   🌐 Instalación: python.org")
        return "official"
    elif "conda" in sys.executable or "anaconda" in sys.executable:
        print("   🐍 Instalación: Conda/Anaconda")
        return "conda"
    else:
        print("   ❓ Instalación: Desconocida")
        return "unknown"

def test_tkinter():
    """Probar importación de tkinter"""
    print("\n🧪 Probando tkinter:")
    
    try:
        import tkinter as tk
        print("   ✅ tkinter importado correctamente")
        
        # Probar creación de ventana
        try:
            root = tk.Tk()
            root.withdraw()  # Ocultar ventana
            print("   ✅ Ventana tkinter creada correctamente")
            root.destroy()
            return True
        except Exception as e:
            print(f"   ❌ Error creando ventana: {e}")
            return False
            
    except ImportError as e:
        print(f"   ❌ Error importando tkinter: {e}")
        
        # Diagnóstico específico
        if "No module named '_tkinter'" in str(e):
            print("   🔍 Diagnóstico: Falta el módulo _tkinter (problema común en Homebrew)")
        elif "No module named 'tkinter'" in str(e):
            print("   🔍 Diagnóstico: tkinter no está instalado")
        
        return False

def check_homebrew():
    """Verificar instalación de Homebrew"""
    print("\n🍺 Verificando Homebrew:")
    
    try:
        result = subprocess.run(["brew", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip().split('\n')[0]
            print(f"   ✅ {version}")
            return True
        else:
            print("   ❌ Homebrew no responde correctamente")
            return False
    except FileNotFoundError:
        print("   ❌ Homebrew no está instalado")
        return False

def check_python_versions():
    """Verificar versiones de Python disponibles"""
    print("\n🔍 Versiones de Python disponibles:")
    
    python_commands = ["python3", "python", "python3.11", "python3.12", "python3.13"]
    available_pythons = []
    
    for cmd in python_commands:
        try:
            result = subprocess.run([cmd, "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                version = result.stdout.strip()
                path_result = subprocess.run([cmd, "-c", "import sys; print(sys.executable)"], 
                                           capture_output=True, text=True)
                path = path_result.stdout.strip() if path_result.returncode == 0 else "Desconocida"
                print(f"   ✅ {cmd}: {version}")
                print(f"      Ruta: {path}")
                available_pythons.append((cmd, version, path))
        except FileNotFoundError:
            continue
    
    return available_pythons

def suggest_solutions(python_type):
    """Sugerir soluciones basadas en el tipo de instalación"""
    print("\n💡 SOLUCIONES RECOMENDADAS:")
    print("=" * 40)
    
    if python_type == "homebrew":
        print("🍺 Para Python de Homebrew:")
        print("   1. Reinstalar Python con tkinter:")
        print("      brew uninstall python@3.13")
        print("      brew install python-tk")
        print()
        print("   2. O instalar tkinter específicamente:")
        print("      brew install tcl-tk")
        print("      brew reinstall python@3.13")
        print()
        print("   3. Usar Python oficial:")
        print("      Descargar desde https://www.python.org/downloads/")
        
    elif python_type == "system":
        print("🍎 Para Python del sistema:")
        print("   1. Instalar Python oficial:")
        print("      Descargar desde https://www.python.org/downloads/")
        print()
        print("   2. O usar Homebrew:")
        print("      brew install python-tk")
        
    elif python_type == "official":
        print("🌐 Python oficial debería incluir tkinter.")
        print("   Si no funciona, reinstalar desde:")
        print("   https://www.python.org/downloads/")
        
    else:
        print("❓ Soluciones generales:")
        print("   1. Instalar Python oficial: https://www.python.org/downloads/")
        print("   2. O usar Homebrew: brew install python-tk")

def create_fix_script():
    """Crear script de reparación automática"""
    print("\n🔧 Creando script de reparación...")
    
    fix_script = '''#!/bin/bash
# Script de reparación automática para tkinter en macOS

echo "🔧 Reparando tkinter en macOS..."

# Verificar Homebrew
if command -v brew &> /dev/null; then
    echo "✅ Homebrew encontrado"
    
    echo "📦 Instalando python-tk..."
    brew install python-tk
    
    echo "🔄 Reinstalando Python..."
    brew reinstall python@3.13 python@3.12 python@3.11 2>/dev/null || true
    
    echo "✅ Reparación completada"
else
    echo "❌ Homebrew no encontrado"
    echo "💡 Instala Python oficial desde: https://www.python.org/downloads/"
fi

echo "🧪 Probando tkinter..."
python3 -c "import tkinter; print('✅ tkinter funciona')" 2>/dev/null || echo "❌ tkinter aún no funciona"
'''
    
    with open("fix_tkinter_macos.sh", "w") as f:
        f.write(fix_script)
    
    os.chmod("fix_tkinter_macos.sh", 0o755)
    print("   ✅ Script creado: fix_tkinter_macos.sh")

def main():
    """Función principal del diagnóstico"""
    print_header()
    
    # Verificar que estamos en macOS
    if platform.system() != "Darwin":
        print("❌ Este diagnóstico es específico para macOS")
        return 1
    
    check_system_info()
    python_type = check_python_installation()
    tkinter_works = test_tkinter()
    homebrew_available = check_homebrew()
    available_pythons = check_python_versions()
    
    print("\n" + "=" * 60)
    print("📋 RESUMEN DEL DIAGNÓSTICO")
    print("=" * 60)
    
    if tkinter_works:
        print("✅ tkinter funciona correctamente")
        print("🎉 La aplicación debería ejecutarse sin problemas")
    else:
        print("❌ tkinter NO funciona")
        print("🔧 Se requiere reparación")
        
        suggest_solutions(python_type)
        create_fix_script()
        
        print("\n🚀 PASOS SIGUIENTES:")
        print("1. Ejecutar: ./fix_tkinter_macos.sh")
        print("2. O seguir las soluciones recomendadas arriba")
        print("3. Luego ejecutar: python3 app_gui.py")
    
    print("\n📞 SOPORTE:")
    print("Si los problemas persisten:")
    print("• Usar Python oficial: https://www.python.org/downloads/")
    print("• Verificar variables de entorno PATH")
    print("• Contactar soporte técnico")
    
    return 0 if tkinter_works else 1

if __name__ == "__main__":
    try:
        exit_code = main()
        input("\nPresiona Enter para continuar...")
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⚠️ Diagnóstico cancelado")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error en diagnóstico: {e}")
        sys.exit(1)
