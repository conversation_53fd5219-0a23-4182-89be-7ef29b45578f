#!/usr/bin/env python3
"""
🍎 Instalador Inteligente para macOS - Sistema de Firmado PDF
Detecta y resuelve automáticamente problemas de Python/tkinter en macOS
"""

import subprocess
import sys
import platform
import os
import time
from pathlib import Path

def print_header():
    """Mostrar header del instalador"""
    print("=" * 60)
    print("🍎 INSTALADOR INTELIGENTE PARA macOS")
    print("🔏 Sistema de Firmado PDF")
    print("=" * 60)

def detect_python_installation():
    """Detectar tipo de instalación de Python"""
    executable = sys.executable
    
    if "Homebrew" in sys.version or "/opt/homebrew" in executable or "/usr/local" in executable:
        return "homebrew"
    elif "/System/Library" in executable:
        return "system"
    elif "python.org" in executable or "/Library/Frameworks" in executable:
        return "official"
    elif "conda" in executable or "anaconda" in executable:
        return "conda"
    else:
        return "unknown"

def test_tkinter():
    """Probar si tkinter funciona"""
    try:
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()
        root.destroy()
        return True
    except ImportError:
        return False
    except Exception:
        return False

def check_homebrew():
    """Verificar si Homebrew está instalado"""
    try:
        result = subprocess.run(["brew", "--version"], capture_output=True, text=True)
        return result.returncode == 0
    except FileNotFoundError:
        return False

def install_homebrew():
    """Instalar Homebrew si no está presente"""
    print("🍺 Instalando Homebrew...")
    
    install_cmd = '/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"'
    
    try:
        subprocess.run(install_cmd, shell=True, check=True)
        print("✅ Homebrew instalado correctamente")
        return True
    except subprocess.CalledProcessError:
        print("❌ Error instalando Homebrew")
        return False

def fix_homebrew_tkinter():
    """Reparar tkinter en instalación de Homebrew"""
    print("🔧 Reparando tkinter en Homebrew Python...")
    
    commands = [
        ["brew", "install", "tcl-tk"],
        ["brew", "install", "python-tk"],
        ["brew", "reinstall", "python@3.13"],
        ["brew", "reinstall", "python@3.12"],
        ["brew", "reinstall", "python@3.11"]
    ]
    
    for cmd in commands:
        try:
            print(f"   Ejecutando: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"   ✅ {cmd[1]} completado")
            else:
                print(f"   ⚠️ {cmd[1]} falló (puede ser normal)")
        except Exception as e:
            print(f"   ⚠️ Error en {cmd[1]}: {e}")
    
    return test_tkinter()

def install_official_python():
    """Guiar instalación de Python oficial"""
    print("🌐 Instalación de Python Oficial:")
    print("   1. Abre: https://www.python.org/downloads/")
    print("   2. Descarga la versión más reciente para macOS")
    print("   3. Ejecuta el instalador .pkg")
    print("   4. Asegúrate de marcar 'Add Python to PATH'")
    print("   5. Reinicia la terminal después de la instalación")
    
    response = input("\n¿Has instalado Python oficial? (s/n): ").lower()
    return response.startswith('s')

def find_working_python():
    """Encontrar una instalación de Python que funcione con tkinter"""
    print("🔍 Buscando Python con tkinter funcional...")
    
    python_commands = [
        "/usr/bin/python3",  # Sistema
        "/Library/Frameworks/Python.framework/Versions/3.13/bin/python3",  # Oficial 3.13
        "/Library/Frameworks/Python.framework/Versions/3.12/bin/python3",  # Oficial 3.12
        "/Library/Frameworks/Python.framework/Versions/3.11/bin/python3",  # Oficial 3.11
        "/opt/homebrew/bin/python3",  # Homebrew ARM
        "/usr/local/bin/python3",     # Homebrew Intel
        "python3"  # PATH
    ]
    
    for cmd in python_commands:
        try:
            # Verificar que existe
            result = subprocess.run([cmd, "--version"], capture_output=True, text=True)
            if result.returncode != 0:
                continue
            
            # Probar tkinter
            test_result = subprocess.run([
                cmd, "-c", "import tkinter; tkinter.Tk().withdraw()"
            ], capture_output=True, text=True)
            
            if test_result.returncode == 0:
                print(f"   ✅ Python funcional encontrado: {cmd}")
                return cmd
            else:
                print(f"   ❌ {cmd}: tkinter no funciona")
                
        except FileNotFoundError:
            continue
    
    return None

def create_launcher_with_python(python_path):
    """Crear launcher que use Python específico"""
    launcher_content = f'''#!/bin/bash

# Launcher específico para Python con tkinter funcional
PYTHON_PATH="{python_path}"

echo "🍎 Sistema de Firmado PDF - macOS"
echo "🐍 Usando Python: $PYTHON_PATH"

# Verificar que Python funciona
if ! "$PYTHON_PATH" --version &> /dev/null; then
    echo "❌ Error: Python no encontrado en $PYTHON_PATH"
    echo "💡 Ejecuta: python3 install_macos.py"
    read -p "Presiona Enter para salir..."
    exit 1
fi

# Verificar tkinter
if ! "$PYTHON_PATH" -c "import tkinter" &> /dev/null; then
    echo "❌ Error: tkinter no funciona"
    echo "💡 Ejecuta: python3 install_macos.py"
    read -p "Presiona Enter para salir..."
    exit 1
fi

# Cambiar al directorio del script
DIR="$( cd "$( dirname "${{BASH_SOURCE[0]}}" )}" &> /dev/null && pwd )"
cd "$DIR"

echo "🚀 Iniciando aplicación..."
"$PYTHON_PATH" app_gui.py

if [ $? -ne 0 ]; then
    echo "❌ Error ejecutando la aplicación"
    read -p "Presiona Enter para continuar..."
fi
'''
    
    with open("start_mac_fixed.command", "w") as f:
        f.write(launcher_content)
    
    os.chmod("start_mac_fixed.command", 0o755)
    print(f"✅ Launcher creado: start_mac_fixed.command")

def install_dependencies_with_python(python_path):
    """Instalar dependencias usando Python específico"""
    print(f"📦 Instalando dependencias con {python_path}...")
    
    dependencies = ["PyMuPDF", "pdfplumber", "pillow", "tqdm"]
    
    for dep in dependencies:
        try:
            print(f"   Instalando {dep}...")
            result = subprocess.run([
                python_path, "-m", "pip", "install", dep, "--upgrade"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"   ✅ {dep} instalado")
            else:
                print(f"   ❌ Error instalando {dep}: {result.stderr}")
                
        except Exception as e:
            print(f"   ❌ Error con {dep}: {e}")

def main():
    """Función principal del instalador"""
    print_header()
    
    # Verificar que estamos en macOS
    if platform.system() != "Darwin":
        print("❌ Este instalador es específico para macOS")
        return 1
    
    print(f"🖥️ Sistema: macOS {platform.mac_ver()[0]} ({platform.machine()})")
    print(f"🐍 Python actual: {sys.version}")
    
    # Probar tkinter actual
    if test_tkinter():
        print("✅ tkinter ya funciona correctamente")
        print("🎉 Procediendo con instalación normal...")
        
        # Instalar dependencias normalmente
        from install import install_dependencies
        if install_dependencies():
            print("✅ Instalación completada")
            return 0
        else:
            print("❌ Error en instalación de dependencias")
            return 1
    
    print("❌ tkinter no funciona - iniciando reparación...")
    
    # Detectar tipo de instalación
    python_type = detect_python_installation()
    print(f"📋 Tipo de instalación: {python_type}")
    
    # Estrategias de reparación
    if python_type == "homebrew":
        print("\n🍺 Detectado Python de Homebrew - intentando reparación...")
        
        if not check_homebrew():
            print("❌ Homebrew no encontrado")
            if input("¿Instalar Homebrew? (s/n): ").lower().startswith('s'):
                if not install_homebrew():
                    print("❌ No se pudo instalar Homebrew")
                    return 1
        
        if fix_homebrew_tkinter():
            print("✅ tkinter reparado en Homebrew")
        else:
            print("❌ No se pudo reparar tkinter en Homebrew")
            print("💡 Recomendación: Instalar Python oficial")
            
            if input("¿Instalar Python oficial? (s/n): ").lower().startswith('s'):
                install_official_python()
    
    elif python_type in ["system", "unknown"]:
        print("\n🍎 Python del sistema detectado")
        print("💡 Recomendación: Usar Python oficial o Homebrew")
        
        choice = input("Elegir: (1) Python oficial, (2) Homebrew, (3) Buscar existente: ")
        
        if choice == "1":
            install_official_python()
        elif choice == "2":
            if not check_homebrew():
                install_homebrew()
            fix_homebrew_tkinter()
        elif choice == "3":
            pass  # Continuar a búsqueda
    
    # Buscar Python funcional
    working_python = find_working_python()
    
    if working_python:
        print(f"✅ Python funcional encontrado: {working_python}")
        
        # Instalar dependencias
        install_dependencies_with_python(working_python)
        
        # Crear launcher específico
        create_launcher_with_python(working_python)
        
        print("\n🎉 INSTALACIÓN COMPLETADA")
        print("=" * 40)
        print("🚀 Para ejecutar la aplicación:")
        print("   • Doble clic en: start_mac_fixed.command")
        print(f"   • O ejecutar: {working_python} app_gui.py")
        
        return 0
    
    else:
        print("\n❌ NO SE ENCONTRÓ PYTHON FUNCIONAL")
        print("=" * 40)
        print("💡 SOLUCIONES:")
        print("1. Instalar Python oficial: https://www.python.org/downloads/")
        print("2. Reinstalar Homebrew Python: brew reinstall python@3.13")
        print("3. Usar conda/miniconda")
        print("\nDespués de instalar, ejecuta este script nuevamente.")
        
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        input("\nPresiona Enter para continuar...")
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⚠️ Instalación cancelada")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error inesperado: {e}")
        input("\nPresiona Enter para salir...")
        sys.exit(1)
