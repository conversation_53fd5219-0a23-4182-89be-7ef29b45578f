# 🚀 Guía de Inicio Rápido

## ⚡ Instalación en 30 segundos

### Windows
1. <PERSON><PERSON><PERSON> todos los archivos
2. **Doble clic** en `start_windows.bat`
3. ¡Listo! 🎉

### Mac
1. <PERSON><PERSON><PERSON> todos los archivos
2. **Doble clic** en `start_mac.command`
3. ¡Listo! 🎉

---

## 📋 Uso en 5 pasos

### 1️⃣ Preparar archivos
- **PDF**: Liquidaciones de remuneraciones
- **Firma**: Imagen PNG con transparencia (recomendado)

### 2️⃣ Abrir aplicación
- La interfaz gráfica se abre automáticamente
- Si no, ejecutar: `python app_gui.py`

### 3️⃣ Seleccionar archivos
- Clic en **"Seleccionar PDF"** → Elegir archivo
- Clic en **"Seleccionar Firma"** → Elegir imagen

### 4️⃣ Analizar
- Clic en **"Analizar PDF"**
- Revisar estadísticas en el log

### 5️⃣ Procesar
- Clic en **"Procesar Todo"**
- Esperar a que termine
- ¡Archivos listos en `liquidaciones_firmadas/`!

---

## 📁 Archivos generados

```
liquidaciones_firmadas/
├── DEPARTAMENTO_EDUCACION/
│   ├── JUAN_PEREZ_pag_001.pdf
│   ├── MARIA_LOPEZ_pag_002.pdf
│   └── ...
├── ADMINISTRACION_MUNICIPAL/
│   └── ...
└── ...

liquidaciones_firmadas_organizadas.zip  ← Archivo comprimido
```

---

## 🆘 Problemas comunes

### ❌ "Error importing required libraries"
**Solución:** Ejecutar `python install.py`

### ❌ "No se encontraron páginas válidas"
**Causa:** El PDF no contiene liquidaciones válidas
**Solución:** Verificar que el PDF tiene texto extraíble

### ❌ Firma mal posicionada
**Solución:** Usar "Ajuste vertical" en la configuración

### ❌ Aplicación no abre (Mac)
**Solución:** 
```bash
chmod +x start_mac.command
```

---

## 💡 Consejos

- ✅ **Mejor formato de firma**: PNG con transparencia
- ✅ **Tamaño recomendado**: 180x70 píxeles
- ✅ **Usar Preview**: Para verificar posición antes de procesar
- ✅ **Modo verbose**: Para ver información detallada
- ✅ **Backup**: Siempre mantén copia del PDF original

---

## 🔧 Instalación manual (si es necesario)

```bash
# 1. Instalar Python 3.7+
# Descargar desde: https://www.python.org/downloads/

# 2. Instalar dependencias
pip install PyMuPDF pdfplumber pillow tqdm

# 3. Ejecutar aplicación
python app_gui.py
```

---

## 📞 Soporte rápido

1. **Revisar log** en la aplicación para errores específicos
2. **Ejecutar con verbose** activado para más información
3. **Verificar archivos** que sean del formato correcto
4. **Reinstalar** dependencias: `python install.py`

---

¡Eso es todo! 🎉 El sistema está diseñado para ser **simple y automático**.

Para más detalles, consulta el `README.md` completo.
