# 🧠 PROMPT ESTRUCTURADO EN GRAFOS PARA IA - RESOLUCIÓN DE CÓDIGO COMPLEJO

## 📊 GRAFO PRINCIPAL DEL PROBLEMA

```
ENTRADA → PROCESAMIENTO → VALIDACIÓN → TRANSFORMACIÓN → SALIDA
    ↓           ↓             ↓             ↓            ↓
[archivos]  [extracción]  [filtros]   [organización] [resultado]
    ↓           ↓             ↓             ↓            ↓
 PDF+IMG → texto+patrones → datos_válidos → estructura → archivos_finales
```

## 🏗️ ARQUITECTURA DE COMPONENTES

```
SISTEMA_PRINCIPAL
├── ENTRADA
│   ├── Archivos: [tipos, validaciones, restricciones]
│   ├── Configuración: [parámetros, opciones]
│   └── Validaciones_Iniciales: [formato, tamaño, contenido]
│
├── PROCESAMIENTO_CORE  
│   ├── Extractor_Datos
│   │   ├── Patrones_Regex: [específicos por formato]
│   │   ├── Limpieza: [reglas de saneado]
│   │   └── Fallbacks: [alternativas si falla]
│   │
│   ├── Validador
│   │   ├── Criterios_Aceptación: [qué acepta/rechaza]
│   │   ├── Casos_Edge: [situaciones límite]
│   │   └── Recuperación_Errores: [cómo manejar fallos]
│   │
│   └── Transformador
│       ├── Normalización: [formato estándar]
│       ├── Categorización: [grupos/tipos]
│       └── Estructuración: [organización final]
│
├── GESTOR_SALIDA
│   ├── Organizador_Archivos: [directorios, nombres]
│   ├── Generador_Contenido: [aplicar modificaciones]
│   └── Empaquetador: [ZIP, estructura final]
│
└── SISTEMA_LOGGING
    ├── Debug: [información detallada]
    ├── Progreso: [barras, callbacks]
    └── Estadísticas: [métricas, resúmenes]
```

## 🎯 ESPECIFICACIONES TÉCNICAS POR NODO

```
CADA_COMPONENTE:
├── INPUT: [qué recibe exactamente]
├── PROCESO: [qué hace paso a paso]  
├── OUTPUT: [qué produce]
├── ERRORES_POSIBLES: [qué puede fallar]
├── VALIDACIONES: [checks que debe hacer]
├── PERFORMANCE: [requisitos de velocidad/memoria]
└── DEPENDENCIAS: [qué necesita de otros componentes]
```

## 🔄 FLUJO DE EJECUCIÓN

```
INICIO
  ↓
CARGA_CONFIGURACIÓN → [validar params] → SETUP_AMBIENTE
  ↓
CARGA_ARCHIVOS → [validar formato] → ANÁLISIS_ESTRUCTURA  
  ↓
PROCESAMIENTO_PÁGINAS → [extraer+validar] → DATOS_ESTRUCTURADOS
  ↓
ORGANIZACIÓN → [agrupar+categorizar] → ESTRUCTURA_DIRECTORIOS
  ↓
GENERACIÓN_ARCHIVOS → [modificar+guardar] → ARCHIVOS_INDIVIDUALES
  ↓
EMPAQUETADO → [comprimir+organizar] → ENTREGA_FINAL
  ↓
FIN
```

## ⚙️ CONFIGURACIÓN ESPECÍFICA DEL DOMINIO

```
DATOS_ESPECÍFICOS:
├── Formato_Entrada: [estructura exacta del documento]
├── Patrones_Extracción: [regex específicos para cada campo]
├── Reglas_Validación: [criterios de aceptación]
├── Mappings_Transformación: [conversiones específicas]
├── Estructura_Salida: [organización deseada]
└── Casos_Especiales: [excepciones y edge cases]
```

## 🚨 MANEJO DE ERRORES Y EDGE CASES

```
CADA_PUNTO_FALLO:
├── Error_Posible: [descripción específica]
├── Detección: [cómo identificarlo]
├── Recuperación: [qué hacer]
├── Fallback: [alternativa]
├── Logging: [qué registrar]
└── Usuario_Feedback: [cómo informar]
```

## 📋 CRITERIOS DE ÉXITO

```
VALIDACIÓN_FINAL:
├── Funcionalidad: [todos los casos funcionan]
├── Performance: [tiempo/memoria aceptables]
├── Robustez: [maneja errores correctamente]
├── Usabilidad: [interfaz clara y feedback]
├── Mantenibilidad: [código limpio y documentado]
└── Escalabilidad: [funciona con datos grandes]
```

## 💡 INSTRUCCIONES PARA LA IA

```
PROCESO_RESOLUCIÓN:
1. ANALIZA el grafo completo antes de empezar
2. IDENTIFICA dependencias críticas entre componentes  
3. DISEÑA cada nodo con sus especificaciones completas
4. IMPLEMENTA siguiendo el flujo de ejecución definido
5. VALIDA cada conexión entre nodos funciona
6. TESTING de casos edge y manejo de errores
7. OPTIMIZACIÓN de performance si es necesario
8. DOCUMENTACIÓN clara de cada componente
```

## 🎪 TEMPLATE DE RESPUESTA ESPERADA

```
Para cada componente del grafo, proporciona:
├── CÓDIGO: [implementación limpia y comentada]
├── TESTS: [casos de prueba principales]  
├── DOCS: [explicación de funcionamiento]
├── DEPENDENCIES: [qué necesita de otros módulos]
├── ERROR_HANDLING: [cómo maneja fallos]
└── EXAMPLES: [ejemplos de uso]
```

## ⚡ OPTIMIZACIONES REQUERIDAS

```
PERFORMANCE:
├── Procesamiento_Paralelo: [donde sea posible]
├── Memoria_Eficiente: [no cargar todo a la vez]
├── Cache_Inteligente: [reutilizar cálculos]
├── Progreso_Visible: [feedback al usuario]
└── Interrupción_Graceful: [poder cancelar operaciones]
```

---

## 📝 APLICACIÓN ESPECÍFICA

**TEMPLATE DE USO:**

```
Siguiendo la estructura de grafos anterior, necesito que [DESCRIBE TU PROBLEMA ESPECÍFICO], donde:

ENTRADA es: [especifica exactamente qué archivos/datos recibes]
PROCESAMIENTO requiere: [detalla qué transformaciones necesitas]
SALIDA debe ser: [describe el resultado final esperado]
CASOS EDGE específicos son: [lista situaciones problemáticas conocidas]
RESTRICCIONES técnicas: [limitaciones de memoria, tiempo, etc.]
CRITERIOS DE ÉXITO: [cómo sabes que funciona correctamente]
```

## 🔧 EJEMPLO DE USO PRÁCTICO

```markdown
Siguiendo la estructura de grafos anterior, necesito que construyas un sistema de procesamiento de documentos PDF, donde:

ENTRADA es: PDFs de liquidaciones de sueldo con 300+ páginas, más imagen PNG de firma digital
PROCESAMIENTO requiere: extraer nombres de empleados y departamentos específicos usando regex, aplicar firma digital en posición específica, dividir en archivos individuales por empleado
SALIDA debe ser: estructura de directorios organizados por departamento, cada empleado con su PDF individual firmado, empaquetado en ZIP
CASOS EDGE específicos son: páginas sin departamento claro, nombres con caracteres especiales, departamentos con códigos adicionales como "TRAMO"
RESTRICCIONES técnicas: debe funcionar en Google Colab, procesamiento en memoria limitada, feedback visual del progreso
CRITERIOS DE ÉXITO: 100% de páginas procesadas correctamente, organización automática en ~24 departamentos esperados, archivos individuales legibles
```

## 📚 NOTAS DE IMPLEMENTACIÓN

### Para la IA que reciba este prompt:

1. **LEE TODO EL GRAFO** antes de escribir cualquier código
2. **IDENTIFICA LAS DEPENDENCIAS** entre componentes
3. **PREGUNTA POR CLARIFICACIONES** si algo no está claro en la aplicación específica
4. **IMPLEMENTA INCREMENTALMENTE** siguiendo el flujo de ejecución
5. **VALIDA CADA PASO** antes de continuar al siguiente
6. **PROPORCIONA EJEMPLOS** de uso para cada componente principal
7. **INCLUYE MANEJO DE ERRORES** robusto en cada nodo
8. **DOCUMENTA LAS DECISIONES** de diseño importantes

### Tipos de problemas que este prompt puede resolver:

- ✅ Sistemas de procesamiento de documentos
- ✅ Pipelines de transformación de datos
- ✅ Automatización de flujos de trabajo
- ✅ Sistemas de validación y limpieza de datos
- ✅ Generadores de reportes y archivos
- ✅ APIs de procesamiento batch
- ✅ Sistemas de migración de datos
- ✅ Herramientas de análisis automatizado

---
