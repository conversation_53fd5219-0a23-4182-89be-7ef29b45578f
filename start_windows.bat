@echo off
title Sistema de Firmado PDF - Windows
color 0A

echo.
echo ===============================================
echo    SISTEMA DE FIRMADO PDF - WINDOWS
echo ===============================================
echo.

REM Verificar Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python no esta instalado
    echo.
    echo Descarga Python desde: https://www.python.org/downloads/
    echo Asegurate de marcar "Add Python to PATH"
    echo.
    pause
    exit /b 1
)

echo Python encontrado: 
python --version

REM Verificar archivos
if not exist "app_gui.py" (
    echo ERROR: No se encuentra app_gui.py
    echo Asegurate de estar en el directorio correcto
    pause
    exit /b 1
)

echo.
echo Iniciando aplicacion...
echo.

REM Ejecutar aplicacion
python app_gui.py

if errorlevel 1 (
    echo.
    echo ERROR: La aplicacion termino con errores
    echo.
    echo Soluciones posibles:
    echo 1. Ejecutar: python install.py
    echo 2. Instalar dependencias: pip install -r requirements.txt
    echo 3. Verificar que Python este actualizado
    echo.
    pause
)

echo.
echo Aplicacion cerrada
pause
