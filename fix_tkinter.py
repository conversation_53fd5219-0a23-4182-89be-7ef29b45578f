#!/usr/bin/env python3
"""
🔧 Reparación Rápida de tkinter - macOS
Script específico para solucionar problemas de tkinter en macOS
"""

import subprocess
import sys
import platform
import os

def print_header():
    """Mostrar header"""
    print("=" * 50)
    print("🔧 REPARACIÓN RÁPIDA DE TKINTER")
    print("=" * 50)

def test_tkinter():
    """Probar tkinter"""
    try:
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()
        root.destroy()
        return True
    except:
        return False

def run_command(cmd, description):
    """Ejecutar comando con descripción"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"   ✅ Completado")
            return True
        else:
            print(f"   ⚠️ Advertencia: {result.stderr[:100]}")
            return False
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def fix_homebrew_tkinter():
    """Reparar tkinter en Homebrew"""
    print("\n🍺 Reparando Homebrew Python...")
    
    commands = [
        ("brew install tcl-tk", "Instalando tcl-tk"),
        ("brew install python-tk", "Instalando python-tk"),
        ("brew reinstall python@3.13", "Reinstalando Python 3.13"),
        ("brew reinstall python@3.12", "Reinstalando Python 3.12"),
        ("brew link --overwrite python@3.13", "Vinculando Python 3.13")
    ]
    
    success_count = 0
    for cmd, desc in commands:
        if run_command(cmd, desc):
            success_count += 1
    
    return success_count > 2  # Al menos 3 comandos exitosos

def install_official_python():
    """Abrir página de Python oficial"""
    print("\n🌐 Abriendo página de Python oficial...")
    
    if platform.system() == "Darwin":
        subprocess.run(["open", "https://www.python.org/downloads/"])
    else:
        print("   Visita: https://www.python.org/downloads/")
    
    print("📋 Instrucciones:")
    print("   1. Descarga Python 3.11+ para macOS")
    print("   2. Ejecuta el instalador .pkg")
    print("   3. Reinicia la terminal")
    print("   4. Ejecuta este script nuevamente")

def main():
    """Función principal"""
    print_header()
    
    if platform.system() != "Darwin":
        print("❌ Este script es específico para macOS")
        return 1
    
    print(f"🖥️ Sistema: macOS {platform.mac_ver()[0]}")
    print(f"🐍 Python: {sys.version}")
    
    # Probar tkinter actual
    if test_tkinter():
        print("✅ tkinter ya funciona correctamente")
        return 0
    
    print("❌ tkinter no funciona - iniciando reparación...")
    
    # Detectar si es Homebrew
    if "Homebrew" in sys.version or "/opt/homebrew" in sys.executable:
        print("📦 Detectado Python de Homebrew")
        
        # Verificar Homebrew
        try:
            subprocess.run(["brew", "--version"], capture_output=True, check=True)
            print("✅ Homebrew disponible")
            
            if fix_homebrew_tkinter():
                print("\n🧪 Probando tkinter después de reparación...")
                if test_tkinter():
                    print("✅ ¡tkinter reparado exitosamente!")
                    print("🎉 Ahora puedes ejecutar: python3 app_gui.py")
                    return 0
                else:
                    print("❌ tkinter aún no funciona")
            
        except:
            print("❌ Homebrew no disponible")
    
    # Si llegamos aquí, la reparación falló
    print("\n❌ Reparación automática falló")
    print("\n💡 SOLUCIÓN RECOMENDADA:")
    print("   Instalar Python oficial que incluye tkinter")
    
    choice = input("\n¿Abrir página de descarga de Python? (s/n): ")
    if choice.lower().startswith('s'):
        install_official_python()
    
    print("\n🔄 ALTERNATIVAS:")
    print("   1. Ejecutar diagnóstico completo: python3 diagnose_macos.py")
    print("   2. Instalador inteligente: python3 install_macos.py")
    print("   3. Usar conda: conda install tk")
    
    return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        input("\nPresiona Enter para continuar...")
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⚠️ Reparación cancelada")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error inesperado: {e}")
        sys.exit(1)
