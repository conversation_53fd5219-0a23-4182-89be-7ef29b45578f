# 🍎 Solución de Problemas en macOS

## ❌ Error: "No module named '_tkinter'"

Este es el error más común en macOS cuando Python fue instalado via Homebrew. **tkinter** es necesario para la interfaz gráfica.

### 🚀 Soluciones Automáticas (Recomendadas)

#### 1️⃣ Reparación Rápida
```bash
python3 fix_tkinter.py
```
- ✅ <PERSON> r<PERSON>pid<PERSON> (2-3 minutos)
- ✅ Repara Homebrew Python
- ✅ Mantiene tu instalación actual

#### 2️⃣ Instalador Inteligente
```bash
python3 install_macos.py
```
- ✅ Detecta automáticamente el problema
- ✅ Encuentra Python alternativo
- ✅ Crea launcher específico

#### 3️⃣ Diagnóstico Completo
```bash
python3 diagnose_macos.py
```
- ✅ Análisis detallado del sistema
- ✅ Recomendaciones específicas
- ✅ Script de reparación personalizado

### 🔧 Soluciones Manuales

#### Para Python de Homebrew:
```bash
# Instalar tkinter
brew install python-tk

# Reinstalar Python
brew reinstall python@3.13

# Verificar
python3 -c "import tkinter; print('✅ tkinter funciona')"
```

#### Instalar Python Oficial (Recomendado):
1. Descargar desde: https://www.python.org/downloads/
2. Ejecutar instalador .pkg
3. Reiniciar terminal
4. Verificar: `python3 -c "import tkinter"`

### 🔍 Identificar tu Instalación de Python

```bash
# Ver información de Python
python3 -c "import sys; print(sys.executable)"
python3 -c "import sys; print(sys.version)"

# Si ves "/opt/homebrew" o "Homebrew" = Homebrew Python
# Si ves "/Library/Frameworks" = Python oficial
# Si ves "/usr/bin" = Python del sistema
```

## 🚀 Inicio Rápido Después de Reparar

### Opción 1: Launcher Automático
```bash
# El script detecta automáticamente problemas
./start_mac.command
```

### Opción 2: Python Específico
```bash
# Si tienes Python oficial
/Library/Frameworks/Python.framework/Versions/3.13/bin/python3 app_gui.py

# Si reparaste Homebrew
python3 app_gui.py
```

### Opción 3: Launcher Personalizado
```bash
# Si el instalador creó uno específico
./start_mac_fixed.command
```

## 🔄 Flujo de Solución Recomendado

1. **Probar reparación rápida**:
   ```bash
   python3 fix_tkinter.py
   ```

2. **Si falla, usar instalador inteligente**:
   ```bash
   python3 install_macos.py
   ```

3. **Si aún falla, instalar Python oficial**:
   - Ir a: https://www.python.org/downloads/
   - Descargar versión más reciente
   - Ejecutar instalador

4. **Verificar funcionamiento**:
   ```bash
   python3 app_gui.py
   ```

## 🆘 Casos Especiales

### Python 3.13.5 de Homebrew
Este es exactamente el caso reportado. Solución:
```bash
# Método 1: Reparar Homebrew
brew install tcl-tk python-tk
brew reinstall python@3.13

# Método 2: Usar Python oficial
# Descargar desde python.org
```

### Múltiples Versiones de Python
```bash
# Listar todas las versiones
ls /Library/Frameworks/Python.framework/Versions/
ls /opt/homebrew/bin/python*

# Probar cada una
/Library/Frameworks/Python.framework/Versions/3.12/bin/python3 -c "import tkinter"
```

### Variables de Entorno
```bash
# Verificar PATH
echo $PATH

# Agregar Python oficial al PATH (en ~/.zshrc o ~/.bash_profile)
export PATH="/Library/Frameworks/Python.framework/Versions/3.13/bin:$PATH"
```

## ✅ Verificación Final

Después de cualquier solución, verificar:

```bash
# 1. Python funciona
python3 --version

# 2. tkinter funciona
python3 -c "import tkinter; tkinter.Tk().withdraw(); print('✅ tkinter OK')"

# 3. Dependencias instaladas
python3 -c "import fitz, pdfplumber, PIL; print('✅ Dependencias OK')"

# 4. Aplicación funciona
python3 app_gui.py
```

## 📞 Soporte Adicional

Si ninguna solución funciona:

1. **Ejecutar diagnóstico completo**:
   ```bash
   python3 diagnose_macos.py > diagnostico.txt
   ```

2. **Usar modo línea de comandos**:
   ```bash
   python3 main.py archivo.pdf firma.png
   ```

3. **Reinstalación completa**:
   - Desinstalar Python actual
   - Instalar Python oficial
   - Ejecutar `python3 install.py`

## 🎯 Resumen de Archivos de Solución

- `fix_tkinter.py` - Reparación rápida
- `install_macos.py` - Instalador inteligente  
- `diagnose_macos.py` - Diagnóstico completo
- `start_mac.command` - Launcher con detección automática
- `start_mac_fixed.command` - Launcher con Python específico (se crea automáticamente)

¡Con estas herramientas, el 99% de los problemas de tkinter en macOS se resuelven automáticamente! 🎉
