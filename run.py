#!/usr/bin/env python3
"""
🚀 Iniciador Simple - Sistema de Firmado PDF
Script de inicio que detecta el modo de ejecución
"""

import sys
import os
from pathlib import Path

def main():
    """Función principal de inicio"""
    print("🔏 Sistema de Firmado PDF")
    print("=" * 40)
    
    # Verificar si existe la GUI
    if Path("app_gui.py").exists():
        print("🖥️ Iniciando interfaz gráfica...")
        try:
            # Importar y ejecutar GUI
            from app_gui import main as gui_main
            gui_main()
        except ImportError as e:
            print(f"❌ Error cargando GUI: {e}")
            print("💡 Ejecuta: python install.py")
            input("Presiona Enter para salir...")
            return 1
        except Exception as e:
            print(f"❌ Error en GUI: {e}")
            input("Presiona Enter para salir...")
            return 1
    
    # Fallback a línea de comandos
    elif Path("main.py").exists():
        print("💻 Modo línea de comandos disponible")
        print("💡 Uso: python main.py archivo.pdf firma.png")
        print("💡 Ayuda: python main.py --help")
        input("Presiona Enter para continuar...")
        return 0
    
    else:
        print("❌ No se encontraron archivos del sistema")
        print("💡 Asegúrate de estar en el directorio correcto")
        input("Presiona Enter para salir...")
        return 1

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n👋 ¡Hasta luego!")
        sys.exit(0)
