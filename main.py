#@title 🚀 Sistema Profesional de Firmado PDF - Instalación Mejorada
!pip install PyMuPDF pdfplumber pillow tqdm ipywidgets -q

import os
import re
import shutil
import zipfile
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Tuple, Optional
import time
import threading
from concurrent.futures import ThreadPoolExecutor

# Librerías principales
import fitz  # PyMuPDF
import pdfplumber
from PIL import Image
from tqdm.auto import tqdm

# Interface de usuario
import ipywidgets as widgets
from IPython.display import display, HTML, clear_output, Image as IPImage
from google.colab import files

print("✅ Sistema profesional instalado correctamente")
print("📚 Librerías cargadas: PyMuPDF, PDFPlumber, Pillow, tqdm, ipywidgets")

#@title 📋 Configuraciones y Clases Core Mejoradas
class Config:
    """Configuración centralizada del sistema"""
    # Configuración de firma
    TARGET_TEXTS = [
        "FIRMA UNIDAD DE PERSONAL",
        "FIRMA:",
        "FIRMA AUTORIZADA", 
        "VALIDACIÓN",
        "FIRMA DEL EMPLEADO",
        "EMPLEADO",
        "TRABAJADOR"
    ]

    # Configuración de posición
    DEFAULT_OFFSET_X = 0
    DEFAULT_OFFSET_Y = 620
    DEFAULT_WIDTH = 180
    DEFAULT_HEIGHT = 70

    # Configuración de validación MEJORADA
    VALIDATION_KEYWORDS = [
        "LIQUIDACIÓN DE REMUNERACIONES",
        "LIQUIDACION DE REMUNERACIONES", 
        "LIQUIDACION",
        "LIQUIDACIÓN",
        "RUT",
        "Rut",
        "HABERES",
        "DESCUENTOS",
        "TOTAL HABERES",
        "TOTAL DESCUENTOS",
        "LÍQUIDO A PAGAR",
        "LIQUIDO A PAGAR"
    ]

    # Configuración de departamentos EXPANDIDA
    SPECIAL_MAPPINGS = {
        ("DIR", "EDUCACION"): "ADMINISTRACION_DAEM",
        ("DIRECCION", "EDUCACION"): "ADMINISTRACION_DAEM",
        ("MUNICIPAL", "EDUCACION"): "ADMINISTRACION_DAEM",
        ("DEPARTAMENTO", "EDUCACION"): "ADMINISTRACION_DAEM"
    }

    # Patrones de limpieza MEJORADOS - evitar capturar TRAMO
    CLEANUP_PATTERNS = [
        r'\s*TRAMO.*',          # Cortar en TRAMO
        r'\s*%.*',              # Cortar en %
        r'\s*-PERS.*',          # Cortar en -PERS
        r'\s*COD\.?\s*\d+.*',   # Cortar en COD seguido de números
        r'\s*CODIGO.*',         # Cortar en CODIGO
        r'\s*PLANTA.*',         # Cortar en PLANTA
        r'\s*CONTRATA.*',       # Cortar en CONTRATA
        r'\s*G\s*N.*',          # Cortar en G N
        r'\s*RBD.*',            # Cortar en RBD
        r'\s*F_?\s*N?[°º]?\s*\d+.*',  # Cortar en F N° números
        r'\s*Nº?\s*\d+.*',      # Cortar en Nº números
        r'\s*-.*',              # Cortar en guión
        r'\s*\d{4,}.*'          # Cortar en números largos (códigos)
    ]

class Logger:
    """Sistema de logging avanzado con niveles"""

    def __init__(self):
        self.messages = []
        self.current_operation = ""
        self.verbose = False

    def set_verbose(self, verbose: bool):
        self.verbose = verbose

    def set_operation(self, operation: str):
        self.current_operation = operation
        if self.verbose:
            print(f"🔄 {operation}")

    def info(self, message: str, show_always: bool = False):
        full_msg = f"ℹ️ {message}"
        self.messages.append(full_msg)
        if self.verbose or show_always:
            print(full_msg)

    def success(self, message: str, show_always: bool = True):
        full_msg = f"✅ {message}"
        self.messages.append(full_msg)
        if self.verbose or show_always:
            print(full_msg)

    def warning(self, message: str, show_always: bool = True):
        full_msg = f"⚠️ {message}"
        self.messages.append(full_msg)
        if self.verbose or show_always:
            print(full_msg)

    def error(self, message: str, show_always: bool = True):
        full_msg = f"❌ {message}"
        self.messages.append(full_msg)
        if self.verbose or show_always:
            print(full_msg)

    def debug(self, message: str):
        full_msg = f"🔍 DEBUG: {message}"
        self.messages.append(full_msg)
        if self.verbose:
            print(full_msg)

    def get_full_log(self) -> str:
        return "\n".join(self.messages)

class TextProcessor:
    """Procesador de texto MEJORADO con mejor extracción"""

    @staticmethod
    def clean_department_name(dept_name: str) -> str:
        """Limpia y normaliza nombres de departamentos ESPECÍFICO para Galvarino"""
        if not dept_name or len(dept_name.strip()) < 2:
            return "SIN_DEPARTAMENTO"

        # Limpiar entrada y aplicar patrones de limpieza PRIMERO
        cleaned = dept_name.strip()
        
        # Aplicar patrones de limpieza para cortar en TRAMO, COD, etc.
        for pattern in Config.CLEANUP_PATTERNS:
            cleaned = re.split(pattern, cleaned, 1, re.IGNORECASE)[0]
        
        # Limpiar espacios adicionales
        cleaned = cleaned.strip()
        
        # Normalizar entrada después de limpieza
        dept_upper = cleaned.upper().strip()
        
        # CASOS ESPECÍFICOS para el formato de Galvarino
        if "DIR. DE EDUCACION" in dept_upper or "DIR DE EDUCACION" in dept_upper:
            return "DIRECCION_EDUCACION"
        elif "DEPARTAMENTO DE EDUCACION" in dept_upper or "DEPARTAMENTO DE EDUCACIÓN" in dept_upper:
            return "DEPARTAMENTO_EDUCACION"
        elif "ADMINISTRATIVO DEM" in dept_upper:
            return "DIRECCION_EDUCACION"
        elif "DOCENTE DEM" in dept_upper:
            return "DIRECCION_EDUCACION"
        elif "AUXILIAR DEM" in dept_upper:
            return "DIRECCION_EDUCACION"

        # Verificar mapeos especiales DESPUÉS de casos específicos
        for keywords, replacement in Config.SPECIAL_MAPPINGS.items():
            if all(keyword in dept_upper for keyword in keywords):
                return replacement

        # Normalizar para nombres de establecimientos
        normalized = cleaned.upper().strip()
        # Remover caracteres especiales pero mantener algunos
        normalized = re.sub(r'[^\w\s\-ÁÉÍÓÚÑÜ\.]', '', normalized)  # Mantener puntos
        normalized = re.sub(r'\s+', '_', normalized)
        normalized = re.sub(r'_+', '_', normalized).strip('_')

        # Casos adicionales después de limpieza
        if "EDUCACION" in normalized or "EDUCACIÓN" in normalized:
            return "DEPARTAMENTO_EDUCACION"
        elif "MUNICIPAL" in normalized:
            return "ADMINISTRACION_MUNICIPAL"
        elif any(word in normalized for word in ["ESCUELA", "LICEO", "COLEGIO", "JARDIN", "JARDÍN"]):
            # Mantener el nombre específico del establecimiento
            return normalized

        # Validar longitud mínima
        if len(normalized) < 3:
            return "DEPARTAMENTO_CORTO"

        return normalized

    @staticmethod
    def clean_employee_name(name: str) -> str:
        """Limpia y normaliza nombres de empleados MEJORADO"""
        if not name or len(name.strip()) < 2:
            return "SIN_NOMBRE"

        # Limpiar caracteres especiales pero mantener acentos y espacios
        cleaned = re.sub(r'[^\w\s\-ÁÉÍÓÚÑÜ]', '', name.upper().strip())
        # Normalizar espacios múltiples
        cleaned = re.sub(r'\s+', '_', cleaned)
        # Remover guiones dobles o múltiples
        cleaned = re.sub(r'_+', '_', cleaned).strip('_')

        # Validar que no sea solo un carácter
        if len(cleaned) < 3:
            return "NOMBRE_CORTO"

        return cleaned

    @staticmethod
    def extract_page_info(page_text: str) -> Dict[str, any]:
        """Extrae información de una página de liquidación MEJORADO"""
        
        # Normalizar texto para búsqueda
        normalized_text = re.sub(r'\s+', ' ', page_text)
        
        # DEBUG: Examinar las primeras líneas del texto
        lines = page_text.split('\n')[:15]  # Primeras 15 líneas
        
        # Patrones MEJORADOS para nombres (más específicos)
        name_patterns = [
            # Patrón principal: Nombre seguido de dos puntos
            r"(?:Nombre|NOMBRE)\s*:?\s*([A-ZÁÉÍÓÚÑÜ][A-ZÁÉÍÓÚÑÜ\s\-]{2,60}?)(?:\s*(?:Rut|RUT|Fecha|Depto|Departamento|Código|COD)|\n|$)",
            # Patrón secundario: línea que empieza con nombre
            r"^([A-ZÁÉÍÓÚÑÜ][A-ZÁÉÍÓÚÑÜ\s\-]{5,50}?)(?:\s*(?:Rut|RUT|\d{1,2}[\.\-]\d{3}[\.\-]\d{3}))",
            # Patrón para nombres después de RUT
            r"(?:RUT|Rut)\s*:?\s*\d+[\.\-]?\d*[\.\-]?\d*[\.\-]?\w?\s*([A-ZÁÉÍÓÚÑÜ][A-ZÁÉÍÓÚÑÜ\s\-]{3,50}?)(?:\s*(?:Depto|Departamento|Fecha)|\n|$)"
        ]

        # Patrones ESPECÍFICOS para el formato de Galvarino - CORREGIDOS
        dept_patterns = [
            # Patrón 1: "Departamento:" seguido del valor PERO cortando en TRAMO
            r"Departamento[\s:]+([^\n\r]+?)(?:\s*(?:TRAMO|RBD|COD|\n|$))",
            
            # Patrón 2: "DEPARTAMENTO DE EDUCACIÓN" en el encabezado
            r"DEPARTAMENTO DE ([A-ZÁÉÍÓÚÑÜ][^\n\r]{3,50}?)(?:\s*\n|$)",
            
            # Patrón 3: Capturar nombres específicos de escuelas/liceos/colegios
            r"((?:ESCUELA|LICEO|COLEGIO|JARDIN|JARDÍN|CENTRO)[^\n\r]+?)(?:\s*(?:TRAMO|RBD|COD|\n|$))",
            
            # Patrón 4: "DIR. DE EDUCACION" pero cortando apropiadamente
            r"(DIR\.?\s*DE\s+[A-ZÁÉÍÓÚÑÜ][A-ZÁÉÍÓÚÑÜ\s]+?)(?:\s*(?:TRAMO|RBD|COD|\n|$))",
            
            # Patrón 5: Líneas que empiecen con "DIR." cortando en TRAMO
            r"^(DIR\.?\s*[A-ZÁÉÍÓÚÑÜ][^\n\r]+?)(?:\s*(?:TRAMO|RBD|COD|\n|$))",
            
            # Patrón 6: Nombres de establecimientos educacionales específicos
            r"^([A-ZÁÉÍÓÚÑÜ][A-ZÁÉÍÓÚÑÜ\s\-\.]{8,60}?)(?:\s*(?:TRAMO|RBD|COD|CODIGO|\d{4,}|\n|$))",
            
            # Patrón 7: Capturar después de palabras clave pero antes de TRAMO
            r"(?:ESTABLECIMIENTO|UNIDAD EDUCATIVA)[\s:]*([^\n\r]+?)(?:\s*(?:TRAMO|RBD|COD|\n|$))"
        ]

        # Buscar nombre con validación MEJORADA
        employee_name = "SIN_NOMBRE"
        raw_name_found = ""

        for i, pattern in enumerate(name_patterns):
            matches = re.finditer(pattern, normalized_text, re.IGNORECASE | re.MULTILINE)
            for match in matches:
                raw_name = match.group(1).strip()
                
                # Filtros de validación MEJORADOS
                if (len(raw_name) >= 3 and len(raw_name) <= 60 and
                    not re.search(r'\d{2}[/\-\.]\d{2}[/\-\.]\d{4}', raw_name) and  # fechas
                    not re.search(r'^\d+[\.\-]\d+[\.\-]\d+', raw_name) and  # RUT al inicio
                    not re.search(r'(?:ingreso|fecha|codigo|cod|rbd|tramo)', raw_name, re.IGNORECASE) and
                    len(re.findall(r'[A-ZÁÉÍÓÚÑÜ]', raw_name)) >= 3):  # Mínimo 3 letras
                    
                    raw_name_found = raw_name
                    employee_name = TextProcessor.clean_employee_name(raw_name)
                    break
            
            if employee_name != "SIN_NOMBRE":
                break

        # Buscar departamento con validación MEJORADA
        department = "SIN_DEPARTAMENTO"
        raw_dept_found = ""

        # NUEVA: Buscar en las primeras líneas del texto
        first_lines = '\n'.join(lines)
        
        for i, pattern in enumerate(dept_patterns):
            # Buscar en texto completo
            matches = re.finditer(pattern, normalized_text, re.IGNORECASE | re.MULTILINE)
            for match in matches:
                raw_dept = match.group(1).strip()
                
                # Filtros de validación MEJORADOS pero MÁS PERMISIVOS
                if (len(raw_dept) >= 3 and len(raw_dept) <= 120 and
                    not re.search(r'\d{2}[/\-\.]\d{2}[/\-\.]\d{4}', raw_dept) and  # fechas
                    not re.search(r'^\d+[\.\-]\d+[\.\-]\d+', raw_dept) and  # RUT al inicio
                    not re.search(r'(?:ingreso|fecha|codigo|cod|rbd)', raw_dept, re.IGNORECASE) and
                    len(re.findall(r'[A-ZÁÉÍÓÚÑÜ]', raw_dept)) >= 3):  # Mínimo 3 letras

                    raw_dept_found = raw_dept
                    department = TextProcessor.clean_department_name(raw_dept)
                    break

            if department != "SIN_DEPARTAMENTO":
                break

class DirectoryManager:
    """Gestor de directorios y organización de archivos NUEVO"""
    
    def __init__(self, base_output_dir: str = "liquidaciones_firmadas"):
        self.base_output_dir = Path(base_output_dir)
        self.department_structure = {}
        self.created_dirs = set()
        
    def setup_base_directory(self):
        """Configura el directorio base"""
        if self.base_output_dir.exists():
            shutil.rmtree(self.base_output_dir)
        self.base_output_dir.mkdir(exist_ok=True)
        
    def create_department_directory(self, department_name: str) -> Path:
        """Crea directorio para un departamento específico"""
        safe_dept_name = self._sanitize_directory_name(department_name)
        dept_dir = self.base_output_dir / safe_dept_name
        
        if safe_dept_name not in self.created_dirs:
            dept_dir.mkdir(exist_ok=True)
            self.created_dirs.add(safe_dept_name)
            
        return dept_dir
    
    def _sanitize_directory_name(self, name: str) -> str:
        """Sanitiza nombres para directorios"""
        # Remover caracteres problemáticos para sistemas de archivos
        sanitized = re.sub(r'[<>:"/\\|?*]', '_', name)
        sanitized = re.sub(r'_{2,}', '_', sanitized)
        sanitized = sanitized.strip('_.')
        
        # Limitar longitud
        if len(sanitized) > 50:
            sanitized = sanitized[:50].rstrip('_')
            
        return sanitized or "DEPARTAMENTO_INVALIDO"
    
    def generate_employee_filename(self, employee_name: str, page_num: int = None) -> str:
        """Genera nombre de archivo para empleado"""
        safe_name = self._sanitize_directory_name(employee_name)
        
        if page_num:
            return f"{safe_name}_P{page_num:03d}_LIQUIDACION.pdf"
        else:
            return f"{safe_name}_LIQUIDACION.pdf"
    
    def get_directory_statistics(self) -> Dict[str, int]:
        """Obtiene estadísticas de directorios creados"""
        stats = {}
        for dept_name in self.created_dirs:
            dept_dir = self.base_output_dir / dept_name
            if dept_dir.exists():
                pdf_count = len(list(dept_dir.glob("*.pdf")))
                stats[dept_name] = pdf_count
        return stats

class SignaturePlacer:
    """Colocador inteligente de firmas MEJORADO"""

    def __init__(self, signature_path: str, width: int = None, height: int = None):
        self.signature_path = Path(signature_path)
        self.width = width or Config.DEFAULT_WIDTH
        self.height = height or Config.DEFAULT_HEIGHT

        # Validar firma
        if not self.signature_path.exists():
            raise FileNotFoundError(f"Firma no encontrada: {signature_path}")

        self._validate_signature()

    def _validate_signature(self):
        """Valida que la firma sea apropiada"""
        try:
            with Image.open(self.signature_path) as img:
                if img.mode != 'RGBA':
                    raise ValueError("La firma debe tener transparencia (formato RGBA)")

                # Verificar transparencia real
                alpha = img.split()[-1]
                if min(alpha.getdata()) == 255:
                    raise ValueError("La firma no tiene píxeles transparentes")

        except Exception as e:
            raise ValueError(f"Error validando firma: {e}")

    def find_position(self, page: fitz.Page, offset_y: float = None) -> Tuple[float, float, bool]:
        """Encuentra la mejor posición para colocar la firma MEJORADO"""

        offset_y = offset_y if offset_y is not None else Config.DEFAULT_OFFSET_Y

        # Buscar textos objetivo con prioridad
        for target_text in Config.TARGET_TEXTS:
            results = page.search_for(target_text)
            if results:
                rect = results[0]
                # Posición más inteligente
                x = rect.x0 + (rect.x1 - rect.x0) / 2 - self.width / 2
                y = rect.y1 + 15  # Offset desde el texto
                
                # Validar que esté dentro de la página
                if x < 0:
                    x = 10
                if x + self.width > page.rect.width:
                    x = page.rect.width - self.width - 10
                    
                return x, y, False

        # Posición de respaldo mejorada
        x = (page.rect.width - self.width) / 2  # Centrado horizontalmente
        y = min(offset_y, page.rect.height - self.height - 20)  # Evitar overflow
        return x, y, True

    def apply_signature(self, page: fitz.Page, x: float, y: float):
        """Aplica la firma en la posición especificada"""

        with open(self.signature_path, "rb") as sig_file:
            signature_bytes = sig_file.read()

        signature_rect = fitz.Rect(x, y, x + self.width, y + self.height)
        page.insert_image(signature_rect, stream=signature_bytes)

        return signature_rect

class PDFProcessor:
    """Procesador principal de PDF COMPLETAMENTE MEJORADO"""

    def __init__(self, pdf_path: str, signature_path: str):
        self.pdf_path = Path(pdf_path)
        self.signature_path = Path(signature_path)
        self.logger = Logger()

        # Validaciones iniciales
        if not self.pdf_path.exists():
            raise FileNotFoundError(f"PDF no encontrado: {pdf_path}")

        self.signer = SignaturePlacer(signature_path)
        self.department_data = defaultdict(list)
        
        # NUEVO: Gestor de directorios
        self.directory_manager = DirectoryManager()
        self.directory_manager.setup_base_directory()

    def analyze_pdf(self, progress_callback=None) -> Tuple[bool, str]:
        """Analiza el PDF y extrae información de departamentos MEJORADO"""

        self.logger.set_operation("Analizando estructura del PDF")
        self.department_data.clear()

        try:
            with pdfplumber.open(self.pdf_path) as pdf:
                total_pages = len(pdf.pages)
                self.logger.info(f"PDF tiene {total_pages} páginas", True)

                valid_pages = 0
                processed_pages = 0

                # Procesar páginas con análisis mejorado
                for i, page in enumerate(tqdm(pdf.pages, desc="Analizando páginas")):

                    if progress_callback:
                        progress_callback(i + 1, total_pages, f"Página {i + 1}")

                    page_num = i + 1
                    processed_pages += 1

                    try:
                        # Extraer texto con configuración mejorada
                        page_text = page.extract_text(
                            x_tolerance=2, 
                            y_tolerance=3,
                            layout=True,
                            x_density=7.25,
                            y_density=13
                        ) or ""

                        if len(page_text.strip()) < 30:
                            self.logger.warning(f"Página {page_num}: Texto insuficiente ({len(page_text)} chars)")
                            continue

                        # Análisis MEJORADO de la página
                        page_info = TextProcessor.extract_page_info(page_text)

                        self.logger.debug(f"P{page_num} - Raw Name: '{page_info['raw_name']}'")
                        self.logger.debug(f"P{page_num} - Raw Dept: '{page_info['raw_dept']}'")
                        self.logger.debug(f"P{page_num} - Score: {page_info['total_score']}")

                        if not page_info["is_valid"]:
                            self.logger.warning(f"Página {page_num}: No parece liquidación (score: {page_info['total_score']})")
                            continue

                        # Validación adicional de nombres válidos - MÁS PERMISIVA
                        if (page_info["employee_name"] in ["SIN_NOMBRE", "NOMBRE_CORTO", "NOMBRE_INVALIDO"]):
                            self.logger.warning(f"Página {page_num}: Nombre inválido o no encontrado")
                            # NO continuar - seguir procesando
                        
                        # PROCESAR SIEMPRE si tiene nombre válido, incluso sin departamento específico
                        if page_info["employee_name"] not in ["SIN_NOMBRE", "NOMBRE_CORTO", "NOMBRE_INVALIDO"]:
                            valid_pages += 1
                            
                            # Almacenar información MEJORADA
                            page_data = {
                                "page_num": page_num,
                                "employee_name": page_info["employee_name"],
                                "raw_name": page_info["raw_name"],
                                "raw_dept": page_info["raw_dept"],
                                "keyword_matches": page_info["keyword_matches"],
                                "total_score": page_info["total_score"]
                            }
                            
                            self.department_data[page_info["department"]].append(page_data)

                            self.logger.info(
                                f"P{page_num}: '{page_info['raw_name']}' → {page_info['department']} (score: {page_info['total_score']})"
                            )
                        else:
                            self.logger.warning(f"Página {page_num}: Sin nombre válido - '{page_info['raw_name']}'")

                        # Debug de primeras líneas para páginas problemáticas
                        if page_num <= 3 or not page_info["is_valid"]:
                            self.logger.debug(f"P{page_num} primeras líneas: {page_info.get('debug_lines', [])[:3]}")

                    except Exception as e:
                        self.logger.error(f"Error en página {page_num}: {e}")
                        continue

                # Mostrar resumen con lista específica de departamentos
                self.logger.success(f"Análisis completado: {valid_pages}/{processed_pages} páginas válidas, {len(self.department_data)} departamentos")
                
                # Mostrar lista completa de departamentos encontrados
                self.logger.info("=== DEPARTAMENTOS ENCONTRADOS ===", True)
                dept_list = sorted(self.department_data.keys())
                for i, dept in enumerate(dept_list, 1):
                    count = len(self.department_data[dept])
                    self.logger.info(f"{i:2d}. {dept}: {count} empleados", True)
                
                # Mostrar muestra de empleados por departamento
                self.logger.info("=== MUESTRA DE EMPLEADOS POR DEPARTAMENTO ===", True)
                for dept, pages in list(self.department_data.items())[:5]:  # Primeros 5 departamentos
                    self.logger.info(f"📁 {dept}:", True)
                    for page_data in pages[:3]:  # Primeros 3 empleados
                        self.logger.info(f"   👤 {page_data['raw_name']} (P{page_data['page_num']})", True)
                    if len(pages) > 3:
                        self.logger.info(f"   ... y {len(pages)-3} más", True)

                return True, f"✅ {valid_pages} páginas procesadas en {len(self.department_data)} departamentos"

        except Exception as e:
            error_msg = f"Error fatal en análisis: {e}"
            self.logger.error(error_msg)
            return False, error_msg

    def create_preview(self, offset_y: float = None) -> Optional[bytes]:
        """Crea una preview de cómo quedará la firma MEJORADO"""

        offset_y = offset_y if offset_y is not None else Config.DEFAULT_OFFSET_Y

        try:
            # Tomar la primera página válida para preview
            with fitz.open(self.pdf_path) as pdf:
                if not self.department_data:
                    return None

                # Obtener primera página de datos
                first_dept = next(iter(self.department_data.values()))
                first_page_num = first_dept[0]["page_num"] - 1

                page = pdf.load_page(first_page_num)

                # Crear copia para preview
                preview_pdf = fitz.open()
                preview_page = preview_pdf.new_page(width=page.rect.width, height=page.rect.height)
                preview_page.show_pdf_page(preview_page.rect, pdf, first_page_num)

                # Aplicar firma
                x, y, is_fallback = self.signer.find_position(preview_page, offset_y)
                self.signer.apply_signature(preview_page, x, y)

                # Convertir a imagen con mejor calidad
                pix = preview_page.get_pixmap(matrix=fitz.Matrix(2.0, 2.0))
                img_bytes = pix.tobytes("png")

                preview_pdf.close()
                return img_bytes

        except Exception as e:
            self.logger.error(f"Error creando preview: {e}")
            return None

    def process_all(self, offset_y: float = None, progress_callback=None) -> Tuple[bool, str]:
        """Procesa todo el PDF aplicando firmas COMPLETAMENTE MEJORADO"""

        offset_y = offset_y if offset_y is not None else Config.DEFAULT_OFFSET_Y

        self.logger.set_operation("Procesando y firmando documentos por departamento")

        try:
            with fitz.open(self.pdf_path) as original_pdf:

                total_departments = len(self.department_data)
                processed_files = 0
                failed_files = 0

                for dept_idx, (dept_name, pages_info) in enumerate(self.department_data.items()):

                    if progress_callback:
                        progress_callback(dept_idx + 1, total_departments, f"Procesando {dept_name}")

                    self.logger.info(f"📁 Procesando departamento: {dept_name} ({len(pages_info)} empleados)", True)

                    # CREAR DIRECTORIO DEL DEPARTAMENTO
                    dept_dir = self.directory_manager.create_department_directory(dept_name)
                    self.logger.info(f"📂 Directorio creado: {dept_dir.name}")

                    # Procesar cada empleado individualmente - HOJA POR HOJA
                    for emp_idx, page_info in enumerate(pages_info):
                        try:
                            page_num = page_info["page_num"]
                            employee_name = page_info["employee_name"]
                            raw_name = page_info.get("raw_name", employee_name)

                            self.logger.info(f"  👤 [{emp_idx+1}/{len(pages_info)}] Procesando: {raw_name} (página {page_num})")

                            # Crear PDF individual - UNA HOJA POR ARCHIVO
                            new_pdf = fitz.open()
                            original_page = original_pdf.load_page(page_num - 1)  # PyMuPDF usa índice base-0
                            
                            # Crear nueva página con las mismas dimensiones
                            new_page = new_pdf.new_page(width=original_page.rect.width, height=original_page.rect.height)
                            
                            # Copiar el contenido de la página original a la nueva
                            new_page.show_pdf_page(new_page.rect, original_pdf, original_page.number)

                            # Aplicar firma
                            x, y, is_fallback = self.signer.find_position(new_page, offset_y)
                            signature_rect = self.signer.apply_signature(new_page, x, y)

                            # GENERAR NOMBRE DE ARCHIVO ÚNICO PARA CADA HOJA
                            filename = self.directory_manager.generate_employee_filename(employee_name, page_num)
                            output_path = dept_dir / filename

                            # Guardar archivo individual (UNA SOLA HOJA)
                            new_pdf.save(str(output_path), garbage=4, deflate=True)
                            new_pdf.close()

                            processed_files += 1
                            self.logger.success(f"     ✓ HOJA INDIVIDUAL guardada: {filename}")

                        except Exception as e:
                            failed_files += 1
                            self.logger.error(f"     ✗ Error procesando {raw_name} (P{page_num}): {e}")
                            if 'new_pdf' in locals():
                                new_pdf.close()
                            continue

                    self.logger.success(f"📁 Departamento {dept_name} completado: {len(pages_info)} archivos individuales generados")

                # Estadísticas finales DETALLADAS
                success_msg = f"✅ Procesamiento HOJA POR HOJA completado: {processed_files} archivos individuales, {failed_files} fallos"
                self.logger.success(success_msg)
                
                # Mostrar estructura final de directorios con conteo
                dir_stats = self.directory_manager.get_directory_statistics()
                self.logger.info("=== ESTRUCTURA FINAL DE DEPARTAMENTOS ===", True)
                self.logger.info(f"Total de departamentos encontrados: {len(dir_stats)}", True)
                self.logger.info("(Esperados: ~24 departamentos según formato Galvarino)", True)
                
                for i, (dept, count) in enumerate(sorted(dir_stats.items()), 1):
                    self.logger.info(f"{i:2d}. 📁 {dept}: {count} archivos individuales", True)
                
                if len(dir_stats) < 20:
                    self.logger.warning("⚠️ Se encontraron menos departamentos de los esperados (~24)", True)
                    self.logger.info("💡 Esto puede indicar que algunos departamentos no están bien diferenciados", True)

                return True, success_msg

        except Exception as e:
            error_msg = f"Error fatal en procesamiento: {e}"
            self.logger.error(error_msg)
            return False, error_msg

    def create_zip(self) -> str:
        """Crea archivo ZIP con todos los documentos organizados MEJORADO"""

        zip_path = "liquidaciones_firmadas_organizadas.zip"

        if Path(zip_path).exists():
            Path(zip_path).unlink()

        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED, compresslevel=6) as zf:
            
            base_dir = self.directory_manager.base_output_dir
            
            for file_path in base_dir.rglob("*.pdf"):
                # Mantener estructura de directorios en el ZIP
                arc_path = file_path.relative_to(base_dir)
                zf.write(file_path, arc_path)
                self.logger.debug(f"Añadido al ZIP: {arc_path}")

        file_size = Path(zip_path).stat().st_size / (1024*1024)  # MB
        self.logger.success(f"ZIP creado: {zip_path} ({file_size:.1f} MB)")
        return zip_path

    def diagnose_pdf_structure(self, max_pages: int = 3) -> Dict[str, any]:
        """Diagnostica la estructura del PDF para debugging NUEVA FUNCIÓN"""
        
        self.logger.info("=== DIAGNÓSTICO DE ESTRUCTURA PDF ===", True)
        
        diagnosis = {
            "sample_pages": [],
            "common_patterns": [],
            "potential_dept_lines": [],
            "recommendations": []
        }
        
        try:
            with pdfplumber.open(self.pdf_path) as pdf:
                pages_to_check = min(max_pages, len(pdf.pages))
                
                for i in range(pages_to_check):
                    page = pdf.pages[i]
                    page_text = page.extract_text(
                        x_tolerance=2, 
                        y_tolerance=3,
                        layout=True
                    ) or ""
                    
                    lines = page_text.split('\n')
                    
                    # Analizar primeras 20 líneas
                    first_lines = lines[:20]
                    
                    page_diagnosis = {
                        "page_num": i + 1,
                        "total_lines": len(lines),
                        "first_20_lines": first_lines,
                        "potential_names": [],
                        "potential_depts": [],
                        "keywords_found": []
                    }
                    
                    # Buscar nombres potenciales
                    for line in first_lines:
                        if re.search(r'[A-ZÁÉÍÓÚÑÜ]{3,}.*[A-ZÁÉÍÓÚÑÜ]{3,}', line):
                            page_diagnosis["potential_names"].append(line.strip())
                    
                    # Buscar departamentos potenciales
                    for line in first_lines:
                        if re.search(r'(?:DEPART|EDUC|MUNIC|DIRECC|ESCUELA|LICEO|COLEGIO)', line, re.IGNORECASE):
                            page_diagnosis["potential_depts"].append(line.strip())
                    
                    # Buscar palabras clave
                    for keyword in Config.VALIDATION_KEYWORDS:
                        if keyword.lower() in page_text.lower():
                            page_diagnosis["keywords_found"].append(keyword)
                    
                    diagnosis["sample_pages"].append(page_diagnosis)
                    
                    self.logger.info(f"📄 Página {i+1} - Líneas: {len(lines)}", True)
                    self.logger.info(f"    Nombres potenciales: {len(page_diagnosis['potential_names'])}", True)
                    self.logger.info(f"    Departamentos potenciales: {len(page_diagnosis['potential_depts'])}", True)
                    self.logger.info(f"    Keywords encontradas: {len(page_diagnosis['keywords_found'])}", True)
                    
                    if page_diagnosis["potential_depts"]:
                        self.logger.info(f"    Departamentos: {page_diagnosis['potential_depts'][:2]}", True)
                
        except Exception as e:
            self.logger.error(f"Error en diagnóstico: {e}")
            
        return diagnosis

    def get_statistics(self) -> Dict[str, any]:
        """Obtiene estadísticas DETALLADAS del procesamiento"""

        total_pages = sum(len(pages) for pages in self.department_data.values())
        
        # Estadísticas por departamento
        dept_details = {}
        for dept, pages in self.department_data.items():
            dept_details[dept] = {
                "empleados": len(pages),
                "nombres": [page["raw_name"] for page in pages],
                "paginas": [page["page_num"] for page in pages]
            }

        stats = {
            "total_departments": len(self.department_data),
            "total_pages": total_pages,
            "departments": {dept: len(pages) for dept, pages in self.department_data.items()},
            "department_details": dept_details,
            "directory_stats": self.directory_manager.get_directory_statistics() if hasattr(self, 'directory_manager') else {}
        }

        return stats

print("✅ Clases core MEJORADAS configuradas correctamente")
print("🆕 Nuevas características:")
print("   📁 Gestión automática de directorios por departamento")
print("   👤 Archivos individuales por empleado")
print("   🔍 Extracción mejorada de nombres y departamentos")
print("   📊 Estadísticas detalladas")

#@title 📤 Carga de Archivos
class FileManager:
    """Gestor de archivos optimizado"""

    @staticmethod
    def setup_environment():
        """Limpia el entorno de trabajo"""

        # Limpiar directorio de salida
        output_dir = Path("liquidaciones_firmadas")
        if output_dir.exists():
            shutil.rmtree(output_dir)

        # Limpiar archivos temporales
        for pattern in ["*.pdf", "*.zip"]:
            for file in Path(".").glob(pattern):
                try:
                    file.unlink()
                except:
                    pass

        print("🧹 Entorno limpiado")

    @staticmethod
    def load_pdf() -> Tuple[Optional[str], str]:
        """Carga archivo PDF con validación"""

        print("📤 Selecciona el archivo PDF con las liquidaciones:")
        uploaded = files.upload()

        if not uploaded:
            return None, "❌ No se seleccionó ningún archivo"

        pdf_path = list(uploaded.keys())[0]

        # Validaciones
        if not pdf_path.lower().endswith('.pdf'):
            return None, "❌ El archivo debe ser un PDF"

        try:
            with pdfplumber.open(pdf_path) as pdf:
                page_count = len(pdf.pages)

            print(f"✅ PDF cargado: '{pdf_path}' ({page_count} páginas)")
            return pdf_path, f"✅ {page_count} páginas cargadas"

        except Exception as e:
            return None, f"❌ Error al abrir PDF: {e}"

    @staticmethod
    def load_signature() -> Tuple[Optional[str], str]:
        """Carga archivo de firma con validación avanzada"""

        print("📤 Selecciona la firma PNG (debe tener fondo transparente):")
        uploaded = files.upload()

        if not uploaded:
            return None, "❌ No se seleccionó ninguna firma"

        sig_path = list(uploaded.keys())[0]

        # Validaciones
        if not sig_path.lower().endswith('.png'):
            return None, "❌ La firma debe ser PNG"

        try:
            with Image.open(sig_path) as img:
                if img.mode != 'RGBA':
                    return None, "❌ La firma debe tener transparencia (RGBA)"

                # Verificar transparencia real
                alpha = img.split()[-1]
                if min(alpha.getdata()) == 255:
                    return None, "❌ La firma no tiene píxeles transparentes"

                width, height = img.size

            print(f"✅ Firma cargada: '{sig_path}' ({width}x{height}px)")
            return sig_path, f"✅ Firma válida ({width}x{height}px)"

        except Exception as e:
            return None, f"❌ Error validando firma: {e}"

# Ejecutar configuración inicial
FileManager.setup_environment()

# Cargar archivos
pdf_path, pdf_status = FileManager.load_pdf()
if pdf_path:
    print(pdf_status)
    signature_path, sig_status = FileManager.load_signature()
    if signature_path:
        print(sig_status)
        print("\n🎯 Archivos cargados correctamente. Continúa con el siguiente paso.")
    else:
        print(f"Error con firma: {sig_status}")
else:
    print(f"Error con PDF: {pdf_status}")

#@title 🔍 Análisis y Preview del PDF MEJORADO
if 'pdf_path' in globals() and pdf_path and 'signature_path' in globals() and signature_path:

    print("🔍 Iniciando análisis MEJORADO del PDF...")
    
    # === NUEVO: DIAGNÓSTICO PREVIO ===
    print("\n" + "="*60)
    print("🔬 DIAGNÓSTICO PREVIO DEL PDF")
    print("="*60)
    
    try:
        # Crear procesador temporal para diagnóstico
        diagnostic_processor = PDFProcessor(pdf_path, signature_path)
        diagnostic_processor.logger.set_verbose(False)
        
        # Ejecutar diagnóstico
        diagnosis = diagnostic_processor.diagnose_pdf_structure(max_pages=3)
        
        print("📋 MUESTRA DEL CONTENIDO:")
        for page_diag in diagnosis["sample_pages"]:
            print(f"\n📄 Página {page_diag['page_num']}:")
            print("   Primeras líneas:")
            for i, line in enumerate(page_diag["first_20_lines"][:8]):
                if line.strip():
                    print(f"      {i+1:2d}: {line.strip()[:80]}")
            
            if page_diag["potential_depts"]:
                print(f"   🏢 Posibles departamentos detectados:")
                for dept in page_diag["potential_depts"][:3]:
                    print(f"      - {dept[:60]}")
            
            if page_diag["potential_names"]:
                print(f"   👤 Posibles nombres detectados:")
                for name in page_diag["potential_names"][:2]:
                    print(f"      - {name[:50]}")
        
        print(f"\n💡 RECOMENDACIONES:")
        print("   - Se detectan nombres correctamente")
        if not any(page["potential_depts"] for page in diagnosis["sample_pages"]):
            print("   - ⚠️ No se detectan departamentos claros")
            print("   - 🔧 Se usarán departamentos genéricos basados en contenido")
        else:
            print("   - ✅ Se detectan posibles departamentos")
            
    except Exception as e:
        print(f"❌ Error en diagnóstico: {e}")

    print(f"\n" + "="*60)
    print("🔄 ANÁLISIS PRINCIPAL")
    print("="*60)

    # Crear procesador
    try:
        processor = PDFProcessor(pdf_path, signature_path)
        processor.logger.set_verbose(True)  # Más detallado para análisis

        # Widget de progreso
        progress_bar = widgets.IntProgress(
            value=0,
            min=0,
            max=100,
            description='Analizando:',
            bar_style='info',
            style={'bar_color': '#20B2AA'},
            orientation='horizontal'
        )

        progress_label = widgets.Label(value="Iniciando análisis...")

        display(widgets.VBox([progress_bar, progress_label]))

        # Función de callback para progreso
        def update_progress(current, total, message):
            progress_bar.value = int((current / total) * 100)
            progress_label.value = f"{message} ({current}/{total})"

        # Ejecutar análisis
        success, message = processor.analyze_pdf(update_progress)

        if success:
            print(f"\n{message}")

            # Mostrar estadísticas DETALLADAS
            stats = processor.get_statistics()
            print(f"\n📊 ESTADÍSTICAS DETALLADAS:")
            print(f"📁 Total departamentos: {stats['total_departments']}")
            print(f"📄 Total empleados: {stats['total_pages']}")

            if stats['total_departments'] > 0:
                print(f"\n📋 DEPARTAMENTOS ENCONTRADOS ({stats['total_departments']} total):")
                print("💡 Formato Galvarino suele tener ~24 departamentos diferentes")
                
                # Mostrar lista numerada de departamentos
                dept_list = sorted(stats['departments'].items())
                for i, (dept, count) in enumerate(dept_list, 1):
                    print(f"{i:2d}. 🏢 {dept}: {count} empleados")
                
                print(f"\n📄 MUESTRA DE EMPLEADOS POR DEPARTAMENTO:")
                for dept, details in list(stats['department_details'].items())[:3]:  # Primeros 3 departamentos
                    print(f"\n🏢 {dept}:")
                    for i, (nombre, pagina) in enumerate(zip(details['nombres'], details['paginas'])):
                        if i < 4:  # Mostrar primeros 4
                            print(f"   👤 {nombre} (página {pagina})")
                        elif i == 4:
                            print(f"   ... y {len(details['nombres'])-4} empleados más")
                            break

                # === SECCIÓN DE PREVIEW Y AJUSTE MEJORADA ===
                print(f"\n" + "="*60)
                print("🎯 PREVIEW Y AJUSTE DE POSICIÓN")
                print("="*60)

                # Slider para ajustar posición Y
                y_slider = widgets.IntSlider(
                    value=Config.DEFAULT_OFFSET_Y,
                    min=300,
                    max=800,
                    step=10,
                    description='Posición Y:',
                    style={'description_width': 'initial'},
                    continuous_update=False
                )

                # Botón para generar preview
                preview_button = widgets.Button(
                    description='👁️ Ver Preview',
                    button_style='info',
                    layout=widgets.Layout(width='150px')
                )

                # Área de preview
                preview_output = widgets.Output()

                def on_preview_click(b):
                    with preview_output:
                        clear_output(wait=True)
                        print("🔄 Generando preview...")

                        preview_bytes = processor.create_preview(y_slider.value)
                        if preview_bytes:
                            print("✅ Preview generado:")
                            display(IPImage(data=preview_bytes, width=500))
                            print(f"📍 Posición Y actual: {y_slider.value}")
                            print("💡 Ajusta el slider y haz click en 'Ver Preview' para reposicionar")
                        else:
                            print("❌ Error generando preview")

                preview_button.on_click(on_preview_click)

                # Mostrar controles de preview
                print("🎛️ Controles de posicionamiento:")
                display(widgets.HBox([y_slider, preview_button]))
                display(preview_output)

                # Generar preview inicial
                initial_preview = processor.create_preview()
                if initial_preview:
                    with preview_output:
                        print("📸 Preview inicial con posición por defecto:")
                        display(IPImage(data=initial_preview, width=500))
                        print(f"📍 Posición Y: {Config.DEFAULT_OFFSET_Y}")

                print(f"\n▶️ Una vez satisfecho con la posición, continúa con el paso 4")
                print(f"📋 Se procesarán HOJA POR HOJA: {stats['total_pages']} archivos individuales")
            else:
                print(f"\n⚠️ No se detectaron departamentos/empleados válidos")
                print(f"📋 Revisa el diagnóstico manual para examinar el contenido")

        else:
            print(f"❌ {message}")
            print("\n📋 Log detallado:")
            print(processor.logger.get_full_log())

    except Exception as e:
        print(f"❌ Error creando procesador: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")

else:
    print("⚠️ Ejecuta primero el paso anterior para cargar los archivos")

#@title 🚀 Procesamiento Final MEJORADO
if ('processor' in globals() and processor and
    hasattr(processor, 'department_data') and processor.department_data):

    print("🚀 SISTEMA LISTO PARA PROCESAMIENTO FINAL")
    print("="*50)

    # Mostrar resumen pre-procesamiento
    stats = processor.get_statistics()
    print(f"📊 Se procesarán HOJA POR HOJA:")
    print(f"   📁 {stats['total_departments']} departamentos")
    print(f"   👥 {stats['total_pages']} empleados (archivos individuales)")
    print(f"   📍 Posición Y de firma: {y_slider.value if 'y_slider' in globals() else Config.DEFAULT_OFFSET_Y}")

    # Mostrar estructura que se creará
    print(f"\n📂 ESTRUCTURA QUE SE CREARÁ (HOJA POR HOJA):")
    for dept, count in stats['departments'].items():
        print(f"   📁 {dept}/ → {count} archivos PDF individuales")

    # Controles finales
    final_y_display = widgets.Label(
        value=f"Posición Y actual: {y_slider.value if 'y_slider' in globals() else Config.DEFAULT_OFFSET_Y}"
    )

    # Checkbox para log detallado
    verbose_check = widgets.Checkbox(
        value=True,  # Activado por defecto para ver el progreso
        description='📋 Mostrar log detallado durante procesamiento'
    )

    # Botón de procesamiento principal
    process_button = widgets.Button(
        description='🚀 PROCESAR TODO',
        button_style='success',
        layout=widgets.Layout(width='200px', height='50px'),
        icon='rocket'
    )

    # Área de resultados
    results_output = widgets.Output()

    def on_process_click(b):
        with results_output:
            clear_output(wait=True)

            print("🚀 INICIANDO PROCESAMIENTO COMPLETO")
            print("="*50)

            start_time = time.time()
            current_y = y_slider.value if 'y_slider' in globals() else Config.DEFAULT_OFFSET_Y

            # Configurar verbosidad
            processor.logger.set_verbose(verbose_check.value)

            try:
                # Barra de progreso para procesamiento
                dept_progress = widgets.IntProgress(
                    value=0,
                    min=0,
                    max=100,
                    description='Progreso:',
                    bar_style='success',
                    style={'bar_color': '#28a745'},
                    orientation='horizontal'
                )

                dept_label = widgets.Label(value="Iniciando procesamiento...")
                progress_display = widgets.VBox([dept_progress, dept_label])
                display(progress_display)

                # Callback de progreso
                def update_dept_progress(current, total, message):
                    dept_progress.value = int((current / total) * 100)
                    dept_label.value = f"{message} ({current}/{total})"

                # Ejecutar procesamiento
                success, message = processor.process_all(current_y, update_dept_progress)

                if success:
                    print(f"\n✅ {message}")

                    # Crear ZIP
                    print("\n📦 Creando archivo ZIP organizado...")
                    zip_file = processor.create_zip()

                    # Estadísticas finales
                    end_time = time.time()
                    duration = end_time - start_time
                    final_stats = processor.get_statistics()

                    print(f"\n🎉 ¡PROCESAMIENTO HOJA POR HOJA COMPLETADO!")
                    print("="*50)
                    print(f"⏱️ Tiempo total: {duration:.1f} segundos")
                    print(f"📁 Departamentos creados: {len(final_stats['directory_stats'])}")
                    print(f"📄 Archivos PDF individuales: {sum(final_stats['directory_stats'].values())}")
                    print(f"📦 Archivo ZIP organizado: {zip_file}")

                    # Mostrar estructura final detallada
                    print(f"\n📂 ESTRUCTURA FINAL CREADA (HOJA POR HOJA):")
                    for i, (dept, count) in enumerate(sorted(final_stats['directory_stats'].items()), 1):
                        print(f"   {i:2d}. 📁 {dept}/ → {count} archivos individuales")
                    
                    if len(final_stats['directory_stats']) < 20:
                        print(f"\n💡 NOTA: Se esperaban ~24 departamentos en formato Galvarino")
                        print(f"   Se encontraron {len(final_stats['directory_stats'])} departamentos")
                        print(f"   Esto puede indicar que algunos están agrupados o no diferenciados")

                    # Botón de descarga
                    print(f"\n⬇️ DESCARGA TU ARCHIVO ORGANIZADO:")
                    files.download(zip_file)

                    if verbose_check.value:
                        print(f"\n📋 LOG COMPLETO DEL PROCESAMIENTO:")
                        print("-" * 50)
                        print(processor.logger.get_full_log())

                else:
                    print(f"❌ {message}")
                    print(f"\n📋 Log de errores:")
                    print(processor.logger.get_full_log())

            except Exception as e:
                print(f"💥 ERROR INESPERADO: {e}")
                import traceback
                print(f"\n🔍 Traceback completo:")
                print(traceback.format_exc())

    process_button.on_click(on_process_click)

    # Mostrar interfaz final
    print(f"\n⚙️ CONFIGURACIÓN FINAL:")
    display(final_y_display)
    display(verbose_check)
    print()
    display(process_button)
    display(results_output)

else:
    print("⚠️ Ejecuta primero el paso 3 para analizar el PDF")
    print("\n🔍 Estado actual:")
    if 'processor' not in globals():
        print("❌ Procesador no inicializado")
    elif not hasattr(processor, 'department_data'):
        print("❌ No hay datos de departamentos")
    elif not processor.department_data:
        print("❌ No se encontraron departamentos válidos")
    else:
        print("✅ Estado parece correcto, reintenta ejecutar esta celda")

class DirectoryManager:
    """Gestor de directorios y organización de archivos NUEVO"""
    
    def __init__(self, base_output_dir: str = "liquidaciones_firmadas"):
        self.base_output_dir = Path(base_output_dir)
        self.department_structure = {}
        self.created_dirs = set()
        
    def setup_base_directory(self):
        """Configura el directorio base"""
        if self.base_output_dir.exists():
            shutil.rmtree(self.base_output_dir)
        self.base_output_dir.mkdir(exist_ok=True)
        
    def create_department_directory(self, department_name: str) -> Path:
        """Crea directorio para un departamento específico"""
        safe_dept_name = self._sanitize_directory_name(department_name)
        dept_dir = self.base_output_dir / safe_dept_name
        
        if safe_dept_name not in self.created_dirs:
            dept_dir.mkdir(exist_ok=True)
            self.created_dirs.add(safe_dept_name)
            
        return dept_dir
    
    def _sanitize_directory_name(self, name: str) -> str:
        """Sanitiza nombres para directorios"""
        # Remover caracteres problemáticos para sistemas de archivos
        sanitized = re.sub(r'[<>:"/\\|?*]', '_', name)
        sanitized = re.sub(r'_{2,}', '_', sanitized)
        sanitized = sanitized.strip('_.')
        
        # Limitar longitud
        if len(sanitized) > 50:
            sanitized = sanitized[:50].rstrip('_')
            
        return sanitized or "DEPARTAMENTO_INVALIDO"
    
    def generate_employee_filename(self, employee_name: str, page_num: int = None) -> str:
        """Genera nombre de archivo para empleado"""
        safe_name = self._sanitize_directory_name(employee_name)
        
        if page_num:
            return f"{safe_name}_P{page_num:03d}_LIQUIDACION.pdf"
        else:
            return f"{safe_name}_LIQUIDACION.pdf"
    
    def get_directory_statistics(self) -> Dict[str, int]:
        """Obtiene estadísticas de directorios creados"""
        stats = {}
        for dept_name in self.created_dirs:
            dept_dir = self.base_output_dir / dept_name
            if dept_dir.exists():
                pdf_count = len(list(dept_dir.glob("*.pdf")))
                stats[dept_name] = pdf_count
        return stats

class SignaturePlacer:
    """Colocador inteligente de firmas MEJORADO"""

    def __init__(self, signature_path: str, width: int = None, height: int = None):
        self.signature_path = Path(signature_path)
        self.width = width or Config.DEFAULT_WIDTH
        self.height = height or Config.DEFAULT_HEIGHT

        # Validar firma
        if not self.signature_path.exists():
            raise FileNotFoundError(f"Firma no encontrada: {signature_path}")

        self._validate_signature()

    def _validate_signature(self):
        """Valida que la firma sea apropiada"""
        try:
            with Image.open(self.signature_path) as img:
                if img.mode != 'RGBA':
                    raise ValueError("La firma debe tener transparencia (formato RGBA)")

                # Verificar transparencia real
                alpha = img.split()[-1]
                if min(alpha.getdata()) == 255:
                    raise ValueError("La firma no tiene píxeles transparentes")

        except Exception as e:
            raise ValueError(f"Error validando firma: {e}")

    def find_position(self, page: fitz.Page, offset_y: float = None) -> Tuple[float, float, bool]:
        """Encuentra la mejor posición para colocar la firma MEJORADO"""

        offset_y = offset_y if offset_y is not None else Config.DEFAULT_OFFSET_Y

        # Buscar textos objetivo con prioridad
        for target_text in Config.TARGET_TEXTS:
            results = page.search_for(target_text)
            if results:
                rect = results[0]
                # Posición más inteligente
                x = rect.x0 + (rect.x1 - rect.x0) / 2 - self.width / 2
                y = rect.y1 + 15  # Offset desde el texto
                
                # Validar que esté dentro de la página
                if x < 0:
                    x = 10
                if x + self.width > page.rect.width:
                    x = page.rect.width - self.width - 10
                    
                return x, y, False

        # Posición de respaldo mejorada
        x = (page.rect.width - self.width) / 2  # Centrado horizontalmente
        y = min(offset_y, page.rect.height - self.height - 20)  # Evitar overflow
        return x, y, True

    def apply_signature(self, page: fitz.Page, x: float, y: float):
        """Aplica la firma en la posición especificada"""

        with open(self.signature_path, "rb") as sig_file:
            signature_bytes = sig_file.read()

        signature_rect = fitz.Rect(x, y, x + self.width, y + self.height)
        page.insert_image(signature_rect, stream=signature_bytes)

        return signature_rect

class PDFProcessor:
    """Procesador principal de PDF COMPLETAMENTE MEJORADO"""

    def __init__(self, pdf_path: str, signature_path: str):
        self.pdf_path = Path(pdf_path)
        self.signature_path = Path(signature_path)
        self.logger = Logger()

        # Validaciones iniciales
        if not self.pdf_path.exists():
            raise FileNotFoundError(f"PDF no encontrado: {pdf_path}")

        self.signer = SignaturePlacer(signature_path)
        self.department_data = defaultdict(list)
        
        # NUEVO: Gestor de directorios
        self.directory_manager = DirectoryManager()
        self.directory_manager.setup_base_directory()

    def analyze_pdf(self, progress_callback=None) -> Tuple[bool, str]:
        """Analiza el PDF y extrae información de departamentos MEJORADO"""

        self.logger.set_operation("Analizando estructura del PDF")
        self.department_data.clear()

        try:
            with pdfplumber.open(self.pdf_path) as pdf:
                total_pages = len(pdf.pages)
                self.logger.info(f"PDF tiene {total_pages} páginas", True)

                valid_pages = 0
                processed_pages = 0

                # Procesar páginas con análisis mejorado
                for i, page in enumerate(tqdm(pdf.pages, desc="Analizando páginas")):

                    if progress_callback:
                        progress_callback(i + 1, total_pages, f"Página {i + 1}")

                    page_num = i + 1
                    processed_pages += 1

                    try:
                        # Extraer texto con configuración mejorada
                        page_text = page.extract_text(
                            x_tolerance=2, 
                            y_tolerance=3,
                            layout=True,
                            x_density=7.25,
                            y_density=13
                        ) or ""

                        if len(page_text.strip()) < 30:
                            self.logger.warning(f"Página {page_num}: Texto insuficiente ({len(page_text)} chars)")
                            continue

                        # Análisis MEJORADO de la página
                        page_info = TextProcessor.extract_page_info(page_text)

                        self.logger.debug(f"P{page_num} - Raw Name: '{page_info['raw_name']}'")
                        self.logger.debug(f"P{page_num} - Raw Dept: '{page_info['raw_dept']}'")
                        self.logger.debug(f"P{page_num} - Score: {page_info['total_score']}")

                        if not page_info["is_valid"]:
                            self.logger.warning(f"Página {page_num}: No parece liquidación (score: {page_info['total_score']})")
                            continue

                        # Validación adicional de nombres válidos
                        if (page_info["employee_name"] in ["SIN_NOMBRE", "NOMBRE_CORTO", "NOMBRE_INVALIDO"] or
                            page_info["department"] in ["SIN_DEPARTAMENTO", "DEPARTAMENTO_CORTO", "DEPARTAMENTO_INVALIDO"]):
                            self.logger.warning(f"Página {page_num}: Información incompleta")
                            continue

                        valid_pages += 1
                        
                        # Almacenar información MEJORADA
                        page_data = {
                            "page_num": page_num,
                            "employee_name": page_info["employee_name"],
                            "raw_name": page_info["raw_name"],
                            "raw_dept": page_info["raw_dept"],
                            "keyword_matches": page_info["keyword_matches"],
                            "total_score": page_info["total_score"]
                        }
                        
                        self.department_data[page_info["department"]].append(page_data)

                        self.logger.info(
                            f"P{page_num}: '{page_info['raw_name']}' → {page_info['department']} (score: {page_info['total_score']})"
                        )

                    except Exception as e:
                        self.logger.error(f"Error en página {page_num}: {e}")
                        continue

                self.logger.success(f"Análisis completado: {valid_pages}/{processed_pages} páginas válidas, {len(self.department_data)} departamentos")
                
                # Mostrar resumen detallado
                self.logger.info("=== RESUMEN DETALLADO ===", True)
                for dept, pages in self.department_data.items():
                    self.logger.info(f"📁 {dept}: {len(pages)} empleados", True)
                    for page_data in pages[:3]:  # Mostrar primeros 3
                        self.logger.info(f"   👤 {page_data['raw_name']}", True)
                    if len(pages) > 3:
                        self.logger.info(f"   ... y {len(pages)-3} más", True)

                return True, f"✅ {valid_pages} páginas procesadas en {len(self.department_data)} departamentos"

        except Exception as e:
            error_msg = f"Error fatal en análisis: {e}"
            self.logger.error(error_msg)
            return False, error_msg

    def create_preview(self, offset_y: float = None) -> Optional[bytes]:
        """Crea una preview de cómo quedará la firma MEJORADO"""

        offset_y = offset_y if offset_y is not None else Config.DEFAULT_OFFSET_Y

        try:
            # Tomar la primera página válida para preview
            with fitz.open(self.pdf_path) as pdf:
                if not self.department_data:
                    return None

                # Obtener primera página de datos
                first_dept = next(iter(self.department_data.values()))
                first_page_num = first_dept[0]["page_num"] - 1

                page = pdf.load_page(first_page_num)

                # Crear copia para preview
                preview_pdf = fitz.open()
                preview_page = preview_pdf.new_page(width=page.rect.width, height=page.rect.height)
                preview_page.show_pdf_page(preview_page.rect, pdf, first_page_num)

                # Aplicar firma
                x, y, is_fallback = self.signer.find_position(preview_page, offset_y)
                self.signer.apply_signature(preview_page, x, y)

                # Convertir a imagen con mejor calidad
                pix = preview_page.get_pixmap(matrix=fitz.Matrix(2.0, 2.0))
                img_bytes = pix.tobytes("png")

                preview_pdf.close()
                return img_bytes

        except Exception as e:
            self.logger.error(f"Error creando preview: {e}")
            return None

    def process_all(self, offset_y: float = None, progress_callback=None) -> Tuple[bool, str]:
        """Procesa todo el PDF aplicando firmas COMPLETAMENTE MEJORADO"""

        offset_y = offset_y if offset_y is not None else Config.DEFAULT_OFFSET_Y

        self.logger.set_operation("Procesando y firmando documentos por departamento")

        try:
            with fitz.open(self.pdf_path) as original_pdf:

                total_departments = len(self.department_data)
                processed_files = 0
                failed_files = 0

                for dept_idx, (dept_name, pages_info) in enumerate(self.department_data.items()):

                    if progress_callback:
                        progress_callback(dept_idx + 1, total_departments, f"Procesando {dept_name}")

                    self.logger.info(f"📁 Procesando departamento: {dept_name} ({len(pages_info)} empleados)", True)

                    # CREAR DIRECTORIO DEL DEPARTAMENTO
                    dept_dir = self.directory_manager.create_department_directory(dept_name)
                    self.logger.info(f"📂 Directorio creado: {dept_dir}")

                    # Procesar cada empleado individualmente
                    for emp_idx, page_info in enumerate(pages_info):
                        try:
                            page_num = page_info["page_num"]
                            employee_name = page_info["employee_name"]
                            raw_name = page_info.get("raw_name", employee_name)

                            self.logger.info(f"  👤 Procesando: {raw_name} (página {page_num})")

                            # Crear PDF individual
                            new_pdf = fitz.open()
                            original_page = original_pdf.load_page(page_num - 1)
                            new_page = new_pdf.new_page(width=original_page.rect.width, height=original_page.rect.height)
                            new_page.show_pdf_page(new_page.rect, original_pdf, original_page.number)

                            # Aplicar firma
                            x, y, is_fallback = self.signer.find_position(new_page, offset_y)
                            signature_rect = self.signer.apply_signature(new_page, x, y)

                            # GENERAR NOMBRE DE ARCHIVO ÚNICO
                            filename = self.directory_manager.generate_employee_filename(employee_name, page_num)
                            output_path = dept_dir / filename

                            # Guardar archivo individual
                            new_pdf.save(str(output_path), garbage=4, deflate=True)
                            new_pdf.close()

                            processed_files += 1
                            self.logger.success(f"     ✓ Guardado: {filename}")

                        except Exception as e:
                            failed_files += 1
                            self.logger.error(f"     ✗ Error procesando {raw_name}: {e}")
                            if 'new_pdf' in locals():
                                new_pdf.close()
                            continue

                    self.logger.success(f"📁 Departamento {dept_name} completado: {len(pages_info)} archivos")

                # Estadísticas finales
                success_msg = f"✅ Procesamiento completado: {processed_files} archivos exitosos, {failed_files} fallos"
                self.logger.success(success_msg)
                
                # Mostrar estructura de directorios
                dir_stats = self.directory_manager.get_directory_statistics()
                self.logger.info("=== ESTRUCTURA FINAL ===", True)
                for dept, count in dir_stats.items():
                    self.logger.info(f"📁 {dept}: {count} archivos", True)

                return True, success_msg

        except Exception as e:
            error_msg = f"Error fatal en procesamiento: {e}"
            self.logger.error(error_msg)
            return False, error_msg

    def create_zip(self) -> str:
        """Crea archivo ZIP con todos los documentos organizados MEJORADO"""

        zip_path = "liquidaciones_firmadas_organizadas.zip"

        if Path(zip_path).exists():
            Path(zip_path).unlink()

        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED, compresslevel=6) as zf:
            
            base_dir = self.directory_manager.base_output_dir
            
            for file_path in base_dir.rglob("*.pdf"):
                # Mantener estructura de directorios en el ZIP
                arc_path = file_path.relative_to(base_dir)
                zf.write(file_path, arc_path)
                self.logger.debug(f"Añadido al ZIP: {arc_path}")

        file_size = Path(zip_path).stat().st_size / (1024*1024)  # MB
        self.logger.success(f"ZIP creado: {zip_path} ({file_size:.1f} MB)")
        return zip_path

    def get_statistics(self) -> Dict[str, any]:
        """Obtiene estadísticas DETALLADAS del procesamiento"""

        total_pages = sum(len(pages) for pages in self.department_data.values())
        
        # Estadísticas por departamento
        dept_details = {}
        for dept, pages in self.department_data.items():
            dept_details[dept] = {
                "empleados": len(pages),
                "nombres": [page["raw_name"] for page in pages],
                "paginas": [page["page_num"] for page in pages]
            }

        stats = {
            "total_departments": len(self.department_data),
            "total_pages": total_pages,
            "departments": {dept: len(pages) for dept, pages in self.department_data.items()},
            "department_details": dept_details,
            "directory_stats": self.directory_manager.get_directory_statistics() if hasattr(self, 'directory_manager') else {}
        }

        return stats

print("✅ Clases core MEJORADAS configuradas correctamente")
print("🆕 Nuevas características:")
print("   📁 Gestión automática de directorios por departamento")
print("   👤 Archivos individuales por empleado")
print("   🔍 Extracción mejorada de nombres y departamentos")
print("   📊 Estadísticas detalladas")

#@title 📤 Carga de Archivos
class FileManager:
    """Gestor de archivos optimizado"""

    @staticmethod
    def setup_environment():
        """Limpia el entorno de trabajo"""

        # Limpiar directorio de salida
        output_dir = Path("liquidaciones_firmadas")
        if output_dir.exists():
            shutil.rmtree(output_dir)

        # Limpiar archivos temporales
        for pattern in ["*.pdf", "*.zip"]:
            for file in Path(".").glob(pattern):
                try:
                    file.unlink()
                except:
                    pass

        print("🧹 Entorno limpiado")

    @staticmethod
    def load_pdf() -> Tuple[Optional[str], str]:
        """Carga archivo PDF con validación"""

        print("📤 Selecciona el archivo PDF con las liquidaciones:")
        uploaded = files.upload()

        if not uploaded:
            return None, "❌ No se seleccionó ningún archivo"

        pdf_path = list(uploaded.keys())[0]

        # Validaciones
        if not pdf_path.lower().endswith('.pdf'):
            return None, "❌ El archivo debe ser un PDF"

        try:
            with pdfplumber.open(pdf_path) as pdf:
                page_count = len(pdf.pages)

            print(f"✅ PDF cargado: '{pdf_path}' ({page_count} páginas)")
            return pdf_path, f"✅ {page_count} páginas cargadas"

        except Exception as e:
            return None, f"❌ Error al abrir PDF: {e}"

    @staticmethod
    def load_signature() -> Tuple[Optional[str], str]:
        """Carga archivo de firma con validación avanzada"""

        print("📤 Selecciona la firma PNG (debe tener fondo transparente):")
        uploaded = files.upload()

        if not uploaded:
            return None, "❌ No se seleccionó ninguna firma"

        sig_path = list(uploaded.keys())[0]

        # Validaciones
        if not sig_path.lower().endswith('.png'):
            return None, "❌ La firma debe ser PNG"

        try:
            with Image.open(sig_path) as img:
                if img.mode != 'RGBA':
                    return None, "❌ La firma debe tener transparencia (RGBA)"

                # Verificar transparencia real
                alpha = img.split()[-1]
                if min(alpha.getdata()) == 255:
                    return None, "❌ La firma no tiene píxeles transparentes"

                width, height = img.size

            print(f"✅ Firma cargada: '{sig_path}' ({width}x{height}px)")
            return sig_path, f"✅ Firma válida ({width}x{height}px)"

        except Exception as e:
            return None, f"❌ Error validando firma: {e}"

# Ejecutar configuración inicial
FileManager.setup_environment()

# Cargar archivos
pdf_path, pdf_status = FileManager.load_pdf()
if pdf_path:
    print(pdf_status)
    signature_path, sig_status = FileManager.load_signature()
    if signature_path:
        print(sig_status)
        print("\n🎯 Archivos cargados correctamente. Continúa con el siguiente paso.")
    else:
        print(f"Error con firma: {sig_status}")
else:
    print(f"Error con PDF: {pdf_status}")

#@title 🔍 Análisis y Preview del PDF MEJORADO
if 'pdf_path' in globals() and pdf_path and 'signature_path' in globals() and signature_path:

    print("🔍 Iniciando análisis MEJORADO del PDF...")

    # Crear procesador
    try:
        processor = PDFProcessor(pdf_path, signature_path)
        processor.logger.set_verbose(True)  # Más detallado para análisis

        # Widget de progreso
        progress_bar = widgets.IntProgress(
            value=0,
            min=0,
            max=100,
            description='Analizando:',
            bar_style='info',
            style={'bar_color': '#20B2AA'},
            orientation='horizontal'
        )

        progress_label = widgets.Label(value="Iniciando análisis...")

        display(widgets.VBox([progress_bar, progress_label]))

        # Función de callback para progreso
        def update_progress(current, total, message):
            progress_bar.value = int((current / total) * 100)
            progress_label.value = f"{message} ({current}/{total})"

        # Ejecutar análisis
        success, message = processor.analyze_pdf(update_progress)

        if success:
            print(f"\n{message}")

            # Mostrar estadísticas DETALLADAS
            stats = processor.get_statistics()
            print(f"\n📊 ESTADÍSTICAS DETALLADAS:")
            print(f"📁 Total departamentos: {stats['total_departments']}")
            print(f"📄 Total empleados: {stats['total_pages']}")

            print(f"\n📋 ESTRUCTURA POR DEPARTAMENTOS:")
            for dept, details in stats['department_details'].items():
                print(f"\n🏢 {dept} ({details['empleados']} empleados):")
                for i, (nombre, pagina) in enumerate(zip(details['nombres'], details['paginas'])):
                    if i < 5:  # Mostrar primeros 5
                        print(f"   👤 {nombre} (página {pagina})")
                    elif i == 5:
                        print(f"   ... y {len(details['nombres'])-5} empleados más")
                        break

            # === SECCIÓN DE PREVIEW Y AJUSTE MEJORADA ===
            print(f"\n" + "="*60)
            print("🎯 PREVIEW Y AJUSTE DE POSICIÓN")
            print("="*60)

            # Slider para ajustar posición Y
            y_slider = widgets.IntSlider(
                value=Config.DEFAULT_OFFSET_Y,
                min=300,
                max=800,
                step=10,
                description='Posición Y:',
                style={'description_width': 'initial'},
                continuous_update=False
            )

            # Botón para generar preview
            preview_button = widgets.Button(
                description='👁️ Ver Preview',
                button_style='info',
                layout=widgets.Layout(width='150px')
            )

            # Área de preview
            preview_output = widgets.Output()

            def on_preview_click(b):
                with preview_output:
                    clear_output(wait=True)
                    print("🔄 Generando preview...")

                    preview_bytes = processor.create_preview(y_slider.value)
                    if preview_bytes:
                        print("✅ Preview generado:")
                        display(IPImage(data=preview_bytes, width=500))
                        print(f"📍 Posición Y actual: {y_slider.value}")
                        print("💡 Ajusta el slider y haz click en 'Ver Preview' para reposicionar")
                    else:
                        print("❌ Error generando preview")

            preview_button.on_click(on_preview_click)

            # Mostrar controles de preview
            print("🎛️ Controles de posicionamiento:")
            display(widgets.HBox([y_slider, preview_button]))
            display(preview_output)

            # Generar preview inicial
            initial_preview = processor.create_preview()
            if initial_preview:
                with preview_output:
                    print("📸 Preview inicial con posición por defecto:")
                    display(IPImage(data=initial_preview, width=500))
                    print(f"📍 Posición Y: {Config.DEFAULT_OFFSET_Y}")

            print(f"\n▶️ Una vez satisfecho con la posición, continúa con el paso 4")

        else:
            print(f"❌ {message}")
            print("\n📋 Log detallado:")
            print(processor.logger.get_full_log())

    except Exception as e:
        print(f"❌ Error creando procesador: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")

else:
    print("⚠️ Ejecuta primero el paso anterior para cargar los archivos")

#@title 🚀 Procesamiento Final MEJORADO
if ('processor' in globals() and processor and
    hasattr(processor, 'department_data') and processor.department_data):

    print("🚀 SISTEMA LISTO PARA PROCESAMIENTO FINAL")
    print("="*50)

    # Mostrar resumen pre-procesamiento
    stats = processor.get_statistics()
    print(f"📊 Se procesarán:")
    print(f"   📁 {stats['total_departments']} departamentos")
    print(f"   👥 {stats['total_pages']} empleados")
    print(f"   📍 Posición Y de firma: {y_slider.value if 'y_slider' in globals() else Config.DEFAULT_OFFSET_Y}")

    # Mostrar estructura que se creará
    print(f"\n📂 ESTRUCTURA QUE SE CREARÁ:")
    for dept, count in stats['departments'].items():
        print(f"   📁 {dept}/ → {count} archivos PDF")

    # Controles finales
    final_y_display = widgets.Label(
        value=f"Posición Y actual: {y_slider.value if 'y_slider' in globals() else Config.DEFAULT_OFFSET_Y}"
    )

    # Checkbox para log detallado
    verbose_check = widgets.Checkbox(
        value=True,  # Activado por defecto para ver el progreso
        description='📋 Mostrar log detallado durante procesamiento'
    )

    # Botón de procesamiento principal
    process_button = widgets.Button(
        description='🚀 PROCESAR TODO',
        button_style='success',
        layout=widgets.Layout(width='200px', height='50px'),
        icon='rocket'
    )

    # Área de resultados
    results_output = widgets.Output()

    def on_process_click(b):
        with results_output:
            clear_output(wait=True)

            print("🚀 INICIANDO PROCESAMIENTO COMPLETO")
            print("="*50)

            start_time = time.time()
            current_y = y_slider.value if 'y_slider' in globals() else Config.DEFAULT_OFFSET_Y

            # Configurar verbosidad
            processor.logger.set_verbose(verbose_check.value)

            try:
                # Barra de progreso para procesamiento
                dept_progress = widgets.IntProgress(
                    value=0,
                    min=0,
                    max=100,
                    description='Progreso:',
                    bar_style='success',
                    style={'bar_color': '#28a745'},
                    orientation='horizontal'
                )

                dept_label = widgets.Label(value="Iniciando procesamiento...")
                progress_display = widgets.VBox([dept_progress, dept_label])
                display(progress_display)

                # Callback de progreso
                def update_dept_progress(current, total, message):
                    dept_progress.value = int((current / total) * 100)
                    dept_label.value = f"{message} ({current}/{total})"

                # Ejecutar procesamiento
                success, message = processor.process_all(current_y, update_dept_progress)

                if success:
                    print(f"\n✅ {message}")

                    # Crear ZIP
                    print("\n📦 Creando archivo ZIP organizado...")
                    zip_file = processor.create_zip()

                    # Estadísticas finales
                    end_time = time.time()
                    duration = end_time - start_time
                    final_stats = processor.get_statistics()

                    print(f"\n🎉 ¡PROCESAMIENTO COMPLETADO!")
                    print("="*50)
                    print(f"⏱️ Tiempo total: {duration:.1f} segundos")
                    print(f"📁 Departamentos creados: {len(final_stats['directory_stats'])}")
                    print(f"📄 Archivos PDF generados: {sum(final_stats['directory_stats'].values())}")
                    print(f"📦 Archivo ZIP: {zip_file}")

                    # Mostrar estructura final
                    print(f"\n📂 ESTRUCTURA FINAL CREADA:")
                    for dept, count in final_stats['directory_stats'].items():
                        print(f"   📁 {dept}/ → {count} archivos")

                    # Botón de descarga
                    print(f"\n⬇️ DESCARGA TU ARCHIVO ORGANIZADO:")
                    files.download(zip_file)

                    if verbose_check.value:
                        print(f"\n📋 LOG COMPLETO DEL PROCESAMIENTO:")
                        print("-" * 50)
                        print(processor.logger.get_full_log())

                else:
                    print(f"❌ {message}")
                    print(f"\n📋 Log de errores:")
                    print(processor.logger.get_full_log())

            except Exception as e:
                print(f"💥 ERROR INESPERADO: {e}")
                import traceback
                print(f"\n🔍 Traceback completo:")
                print(traceback.format_exc())

    process_button.on_click(on_process_click)

    # Mostrar interfaz final
    print(f"\n⚙️ CONFIGURACIÓN FINAL:")
    display(final_y_display)
    display(verbose_check)
    print()
    display(process_button)
    display(results_output)

else:
    print("⚠️ Ejecuta primero el paso 3 para analizar el PDF")
    print("\n🔍 Estado actual:")
    if 'processor' not in globals():
        print("❌ Procesador no inicializado")
    elif not hasattr(processor, 'department_data'):
        print("❌ No hay datos de departamentos")
    elif not processor.department_data:
        print("❌ No se encontraron departamentos válidos")
    else:
        print("✅ Estado parece correcto, reintenta ejecutar esta celda")
            
            if department != "SIN_DEPARTAMENTO":
                break
            
            # También buscar en primeras líneas
            matches = re.finditer(pattern, first_lines, re.IGNORECASE | re.MULTILINE)
            for match in matches:
                raw_dept = match.group(1).strip()
                
                if (len(raw_dept) >= 3 and len(raw_dept) <= 120 and
                    not re.search(r'\d{2}[/\-\.]\d{2}[/\-\.]\d{4}', raw_dept) and
                    not re.search(r'^\d+[\.\-]\d+[\.\-]\d+', raw_dept) and  # RUT al inicio
                    not re.search(r'(?:ingreso|fecha|codigo|cod|rbd)', raw_dept, re.IGNORECASE) and
                    len(re.findall(r'[A-ZÁÉÍÓÚÑÜ]', raw_dept)) >= 3):  # Mínimo 3 letras

                    raw_dept_found = raw_dept
                    department = TextProcessor.clean_department_name(raw_dept)
                    break

            if department != "SIN_DEPARTAMENTO":
                break

class DirectoryManager:
    """Gestor de directorios y organización de archivos NUEVO"""
    
    def __init__(self, base_output_dir: str = "liquidaciones_firmadas"):
        self.base_output_dir = Path(base_output_dir)
        self.department_structure = {}
        self.created_dirs = set()
        
    def setup_base_directory(self):
        """Configura el directorio base"""
        if self.base_output_dir.exists():
            shutil.rmtree(self.base_output_dir)
        self.base_output_dir.mkdir(exist_ok=True)
        
    def create_department_directory(self, department_name: str) -> Path:
        """Crea directorio para un departamento específico"""
        safe_dept_name = self._sanitize_directory_name(department_name)
        dept_dir = self.base_output_dir / safe_dept_name
        
        if safe_dept_name not in self.created_dirs:
            dept_dir.mkdir(exist_ok=True)
            self.created_dirs.add(safe_dept_name)
            
        return dept_dir
    
    def _sanitize_directory_name(self, name: str) -> str:
        """Sanitiza nombres para directorios"""
        # Remover caracteres problemáticos para sistemas de archivos
        sanitized = re.sub(r'[<>:"/\\|?*]', '_', name)
        sanitized = re.sub(r'_{2,}', '_', sanitized)
        sanitized = sanitized.strip('_.')
        
        # Limitar longitud
        if len(sanitized) > 50:
            sanitized = sanitized[:50].rstrip('_')
            
        return sanitized or "DEPARTAMENTO_INVALIDO"
    
    def generate_employee_filename(self, employee_name: str, page_num: int = None) -> str:
        """Genera nombre de archivo para empleado"""
        safe_name = self._sanitize_directory_name(employee_name)
        
        if page_num:
            return f"{safe_name}_P{page_num:03d}_LIQUIDACION.pdf"
        else:
            return f"{safe_name}_LIQUIDACION.pdf"
    
    def get_directory_statistics(self) -> Dict[str, int]:
        """Obtiene estadísticas de directorios creados"""
        stats = {}
        for dept_name in self.created_dirs:
            dept_dir = self.base_output_dir / dept_name
            if dept_dir.exists():
                pdf_count = len(list(dept_dir.glob("*.pdf")))
                stats[dept_name] = pdf_count
        return stats

class SignaturePlacer:
    """Colocador inteligente de firmas MEJORADO"""

    def __init__(self, signature_path: str, width: int = None, height: int = None):
        self.signature_path = Path(signature_path)
        self.width = width or Config.DEFAULT_WIDTH
        self.height = height or Config.DEFAULT_HEIGHT

        # Validar firma
        if not self.signature_path.exists():
            raise FileNotFoundError(f"Firma no encontrada: {signature_path}")

        self._validate_signature()

    def _validate_signature(self):
        """Valida que la firma sea apropiada"""
        try:
            with Image.open(self.signature_path) as img:
                if img.mode != 'RGBA':
                    raise ValueError("La firma debe tener transparencia (formato RGBA)")

                # Verificar transparencia real
                alpha = img.split()[-1]
                if min(alpha.getdata()) == 255:
                    raise ValueError("La firma no tiene píxeles transparentes")

        except Exception as e:
            raise ValueError(f"Error validando firma: {e}")

    def find_position(self, page: fitz.Page, offset_y: float = None) -> Tuple[float, float, bool]:
        """Encuentra la mejor posición para colocar la firma MEJORADO"""

        offset_y = offset_y if offset_y is not None else Config.DEFAULT_OFFSET_Y

        # Buscar textos objetivo con prioridad
        for target_text in Config.TARGET_TEXTS:
            results = page.search_for(target_text)
            if results:
                rect = results[0]
                # Posición más inteligente
                x = rect.x0 + (rect.x1 - rect.x0) / 2 - self.width / 2
                y = rect.y1 + 15  # Offset desde el texto
                
                # Validar que esté dentro de la página
                if x < 0:
                    x = 10
                if x + self.width > page.rect.width:
                    x = page.rect.width - self.width - 10
                    
                return x, y, False

        # Posición de respaldo mejorada
        x = (page.rect.width - self.width) / 2  # Centrado horizontalmente
        y = min(offset_y, page.rect.height - self.height - 20)  # Evitar overflow
        return x, y, True

    def apply_signature(self, page: fitz.Page, x: float, y: float):
        """Aplica la firma en la posición especificada"""

        with open(self.signature_path, "rb") as sig_file:
            signature_bytes = sig_file.read()

        signature_rect = fitz.Rect(x, y, x + self.width, y + self.height)
        page.insert_image(signature_rect, stream=signature_bytes)

        return signature_rect

class PDFProcessor:
    """Procesador principal de PDF COMPLETAMENTE MEJORADO"""

    def __init__(self, pdf_path: str, signature_path: str):
        self.pdf_path = Path(pdf_path)
        self.signature_path = Path(signature_path)
        self.logger = Logger()

        # Validaciones iniciales
        if not self.pdf_path.exists():
            raise FileNotFoundError(f"PDF no encontrado: {pdf_path}")

        self.signer = SignaturePlacer(signature_path)
        self.department_data = defaultdict(list)
        
        # NUEVO: Gestor de directorios
        self.directory_manager = DirectoryManager()
        self.directory_manager.setup_base_directory()

    def analyze_pdf(self, progress_callback=None) -> Tuple[bool, str]:
        """Analiza el PDF y extrae información de departamentos MEJORADO"""

        self.logger.set_operation("Analizando estructura del PDF")
        self.department_data.clear()

        try:
            with pdfplumber.open(self.pdf_path) as pdf:
                total_pages = len(pdf.pages)
                self.logger.info(f"PDF tiene {total_pages} páginas", True)

                valid_pages = 0
                processed_pages = 0

                # Procesar páginas con análisis mejorado
                for i, page in enumerate(tqdm(pdf.pages, desc="Analizando páginas")):

                    if progress_callback:
                        progress_callback(i + 1, total_pages, f"Página {i + 1}")

                    page_num = i + 1
                    processed_pages += 1

                    try:
                        # Extraer texto con configuración mejorada
                        page_text = page.extract_text(
                            x_tolerance=2, 
                            y_tolerance=3,
                            layout=True,
                            x_density=7.25,
                            y_density=13
                        ) or ""

                        if len(page_text.strip()) < 30:
                            self.logger.warning(f"Página {page_num}: Texto insuficiente ({len(page_text)} chars)")
                            continue

                        # Análisis MEJORADO de la página
                        page_info = TextProcessor.extract_page_info(page_text)

                        self.logger.debug(f"P{page_num} - Raw Name: '{page_info['raw_name']}'")
                        self.logger.debug(f"P{page_num} - Raw Dept: '{page_info['raw_dept']}'")
                        self.logger.debug(f"P{page_num} - Score: {page_info['total_score']}")

                        if not page_info["is_valid"]:
                            self.logger.warning(f"Página {page_num}: No parece liquidación (score: {page_info['total_score']})")
                            continue

                        # Validación adicional de nombres válidos
                        if (page_info["employee_name"] in ["SIN_NOMBRE", "NOMBRE_CORTO", "NOMBRE_INVALIDO"] or
                            page_info["department"] in ["SIN_DEPARTAMENTO", "DEPARTAMENTO_CORTO", "DEPARTAMENTO_INVALIDO"]):
                            self.logger.warning(f"Página {page_num}: Información incompleta")
                            continue

                        valid_pages += 1
                        
                        # Almacenar información MEJORADA
                        page_data = {
                            "page_num": page_num,
                            "employee_name": page_info["employee_name"],
                            "raw_name": page_info["raw_name"],
                            "raw_dept": page_info["raw_dept"],
                            "keyword_matches": page_info["keyword_matches"],
                            "total_score": page_info["total_score"]
                        }
                        
                        self.department_data[page_info["department"]].append(page_data)

                        self.logger.info(
                            f"P{page_num}: '{page_info['raw_name']}' → {page_info['department']} (score: {page_info['total_score']})"
                        )

                    except Exception as e:
                        self.logger.error(f"Error en página {page_num}: {e}")
                        continue

                self.logger.success(f"Análisis completado: {valid_pages}/{processed_pages} páginas válidas, {len(self.department_data)} departamentos")
                
                # Mostrar resumen detallado
                self.logger.info("=== RESUMEN DETALLADO ===", True)
                for dept, pages in self.department_data.items():
                    self.logger.info(f"📁 {dept}: {len(pages)} empleados", True)
                    for page_data in pages[:3]:  # Mostrar primeros 3
                        self.logger.info(f"   👤 {page_data['raw_name']}", True)
                    if len(pages) > 3:
                        self.logger.info(f"   ... y {len(pages)-3} más", True)

                return True, f"✅ {valid_pages} páginas procesadas en {len(self.department_data)} departamentos"

        except Exception as e:
            error_msg = f"Error fatal en análisis: {e}"
            self.logger.error(error_msg)
            return False, error_msg

    def create_preview(self, offset_y: float = None) -> Optional[bytes]:
        """Crea una preview de cómo quedará la firma MEJORADO"""

        offset_y = offset_y if offset_y is not None else Config.DEFAULT_OFFSET_Y

        try:
            # Tomar la primera página válida para preview
            with fitz.open(self.pdf_path) as pdf:
                if not self.department_data:
                    return None

                # Obtener primera página de datos
                first_dept = next(iter(self.department_data.values()))
                first_page_num = first_dept[0]["page_num"] - 1

                page = pdf.load_page(first_page_num)

                # Crear copia para preview
                preview_pdf = fitz.open()
                preview_page = preview_pdf.new_page(width=page.rect.width, height=page.rect.height)
                preview_page.show_pdf_page(preview_page.rect, pdf, first_page_num)

                # Aplicar firma
                x, y, is_fallback = self.signer.find_position(preview_page, offset_y)
                self.signer.apply_signature(preview_page, x, y)

                # Convertir a imagen con mejor calidad
                pix = preview_page.get_pixmap(matrix=fitz.Matrix(2.0, 2.0))
                img_bytes = pix.tobytes("png")

                preview_pdf.close()
                return img_bytes

        except Exception as e:
            self.logger.error(f"Error creando preview: {e}")
            return None

    def process_all(self, offset_y: float = None, progress_callback=None) -> Tuple[bool, str]:
        """Procesa todo el PDF aplicando firmas COMPLETAMENTE MEJORADO"""

        offset_y = offset_y if offset_y is not None else Config.DEFAULT_OFFSET_Y

        self.logger.set_operation("Procesando y firmando documentos por departamento")

        try:
            with fitz.open(self.pdf_path) as original_pdf:

                total_departments = len(self.department_data)
                processed_files = 0
                failed_files = 0

                for dept_idx, (dept_name, pages_info) in enumerate(self.department_data.items()):

                    if progress_callback:
                        progress_callback(dept_idx + 1, total_departments, f"Procesando {dept_name}")

                    self.logger.info(f"📁 Procesando departamento: {dept_name} ({len(pages_info)} empleados)", True)

                    # CREAR DIRECTORIO DEL DEPARTAMENTO
                    dept_dir = self.directory_manager.create_department_directory(dept_name)
                    self.logger.info(f"📂 Directorio creado: {dept_dir}")

                    # Procesar cada empleado individualmente
                    for emp_idx, page_info in enumerate(pages_info):
                        try:
                            page_num = page_info["page_num"]
                            employee_name = page_info["employee_name"]
                            raw_name = page_info.get("raw_name", employee_name)

                            self.logger.info(f"  👤 Procesando: {raw_name} (página {page_num})")

                            # Crear PDF individual
                            new_pdf = fitz.open()
                            original_page = original_pdf.load_page(page_num - 1)
                            new_page = new_pdf.new_page(width=original_page.rect.width, height=original_page.rect.height)
                            new_page.show_pdf_page(new_page.rect, original_pdf, original_page.number)

                            # Aplicar firma
                            x, y, is_fallback = self.signer.find_position(new_page, offset_y)
                            signature_rect = self.signer.apply_signature(new_page, x, y)

                            # GENERAR NOMBRE DE ARCHIVO ÚNICO
                            filename = self.directory_manager.generate_employee_filename(employee_name, page_num)
                            output_path = dept_dir / filename

                            # Guardar archivo individual
                            new_pdf.save(str(output_path), garbage=4, deflate=True)
                            new_pdf.close()

                            processed_files += 1
                            self.logger.success(f"     ✓ Guardado: {filename}")

                        except Exception as e:
                            failed_files += 1
                            self.logger.error(f"     ✗ Error procesando {raw_name}: {e}")
                            if 'new_pdf' in locals():
                                new_pdf.close()
                            continue

                    self.logger.success(f"📁 Departamento {dept_name} completado: {len(pages_info)} archivos")

                # Estadísticas finales
                success_msg = f"✅ Procesamiento completado: {processed_files} archivos exitosos, {failed_files} fallos"
                self.logger.success(success_msg)
                
                # Mostrar estructura de directorios
                dir_stats = self.directory_manager.get_directory_statistics()
                self.logger.info("=== ESTRUCTURA FINAL ===", True)
                for dept, count in dir_stats.items():
                    self.logger.info(f"📁 {dept}: {count} archivos", True)

                return True, success_msg

        except Exception as e:
            error_msg = f"Error fatal en procesamiento: {e}"
            self.logger.error(error_msg)
            return False, error_msg

    def create_zip(self) -> str:
        """Crea archivo ZIP con todos los documentos organizados MEJORADO"""

        zip_path = "liquidaciones_firmadas_organizadas.zip"

        if Path(zip_path).exists():
            Path(zip_path).unlink()

        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED, compresslevel=6) as zf:
            
            base_dir = self.directory_manager.base_output_dir
            
            for file_path in base_dir.rglob("*.pdf"):
                # Mantener estructura de directorios en el ZIP
                arc_path = file_path.relative_to(base_dir)
                zf.write(file_path, arc_path)
                self.logger.debug(f"Añadido al ZIP: {arc_path}")

        file_size = Path(zip_path).stat().st_size / (1024*1024)  # MB
        self.logger.success(f"ZIP creado: {zip_path} ({file_size:.1f} MB)")
        return zip_path

    def get_statistics(self) -> Dict[str, any]:
        """Obtiene estadísticas DETALLADAS del procesamiento"""

        total_pages = sum(len(pages) for pages in self.department_data.values())
        
        # Estadísticas por departamento
        dept_details = {}
        for dept, pages in self.department_data.items():
            dept_details[dept] = {
                "empleados": len(pages),
                "nombres": [page["raw_name"] for page in pages],
                "paginas": [page["page_num"] for page in pages]
            }

        stats = {
            "total_departments": len(self.department_data),
            "total_pages": total_pages,
            "departments": {dept: len(pages) for dept, pages in self.department_data.items()},
            "department_details": dept_details,
            "directory_stats": self.directory_manager.get_directory_statistics() if hasattr(self, 'directory_manager') else {}
        }

        return stats

print("✅ Clases core MEJORADAS configuradas correctamente")
print("🆕 Nuevas características:")
print("   📁 Gestión automática de directorios por departamento")
print("   👤 Archivos individuales por empleado")
print("   🔍 Extracción mejorada de nombres y departamentos")
print("   📊 Estadísticas detalladas")

#@title 📤 Carga de Archivos
class FileManager:
    """Gestor de archivos optimizado"""

    @staticmethod
    def setup_environment():
        """Limpia el entorno de trabajo"""

        # Limpiar directorio de salida
        output_dir = Path("liquidaciones_firmadas")
        if output_dir.exists():
            shutil.rmtree(output_dir)

        # Limpiar archivos temporales
        for pattern in ["*.pdf", "*.zip"]:
            for file in Path(".").glob(pattern):
                try:
                    file.unlink()
                except:
                    pass

        print("🧹 Entorno limpiado")

    @staticmethod
    def load_pdf() -> Tuple[Optional[str], str]:
        """Carga archivo PDF con validación"""

        print("📤 Selecciona el archivo PDF con las liquidaciones:")
        uploaded = files.upload()

        if not uploaded:
            return None, "❌ No se seleccionó ningún archivo"

        pdf_path = list(uploaded.keys())[0]

        # Validaciones
        if not pdf_path.lower().endswith('.pdf'):
            return None, "❌ El archivo debe ser un PDF"

        try:
            with pdfplumber.open(pdf_path) as pdf:
                page_count = len(pdf.pages)

            print(f"✅ PDF cargado: '{pdf_path}' ({page_count} páginas)")
            return pdf_path, f"✅ {page_count} páginas cargadas"

        except Exception as e:
            return None, f"❌ Error al abrir PDF: {e}"

    @staticmethod
    def load_signature() -> Tuple[Optional[str], str]:
        """Carga archivo de firma con validación avanzada"""

        print("📤 Selecciona la firma PNG (debe tener fondo transparente):")
        uploaded = files.upload()

        if not uploaded:
            return None, "❌ No se seleccionó ninguna firma"

        sig_path = list(uploaded.keys())[0]

        # Validaciones
        if not sig_path.lower().endswith('.png'):
            return None, "❌ La firma debe ser PNG"

        try:
            with Image.open(sig_path) as img:
                if img.mode != 'RGBA':
                    return None, "❌ La firma debe tener transparencia (RGBA)"

                # Verificar transparencia real
                alpha = img.split()[-1]
                if min(alpha.getdata()) == 255:
                    return None, "❌ La firma no tiene píxeles transparentes"

                width, height = img.size

            print(f"✅ Firma cargada: '{sig_path}' ({width}x{height}px)")
            return sig_path, f"✅ Firma válida ({width}x{height}px)"

        except Exception as e:
            return None, f"❌ Error validando firma: {e}"

# Ejecutar configuración inicial
FileManager.setup_environment()

# Cargar archivos
pdf_path, pdf_status = FileManager.load_pdf()
if pdf_path:
    print(pdf_status)
    signature_path, sig_status = FileManager.load_signature()
    if signature_path:
        print(sig_status)
        print("\n🎯 Archivos cargados correctamente. Continúa con el siguiente paso.")
    else:
        print(f"Error con firma: {sig_status}")
else:
    print(f"Error con PDF: {pdf_status}")

#@title 🔍 Análisis y Preview del PDF MEJORADO
if 'pdf_path' in globals() and pdf_path and 'signature_path' in globals() and signature_path:

    print("🔍 Iniciando análisis MEJORADO del PDF...")

    # Crear procesador
    try:
        processor = PDFProcessor(pdf_path, signature_path)
        processor.logger.set_verbose(True)  # Más detallado para análisis

        # Widget de progreso
        progress_bar = widgets.IntProgress(
            value=0,
            min=0,
            max=100,
            description='Analizando:',
            bar_style='info',
            style={'bar_color': '#20B2AA'},
            orientation='horizontal'
        )

        progress_label = widgets.Label(value="Iniciando análisis...")

        display(widgets.VBox([progress_bar, progress_label]))

        # Función de callback para progreso
        def update_progress(current, total, message):
            progress_bar.value = int((current / total) * 100)
            progress_label.value = f"{message} ({current}/{total})"

        # Ejecutar análisis
        success, message = processor.analyze_pdf(update_progress)

        if success:
            print(f"\n{message}")

            # Mostrar estadísticas DETALLADAS
            stats = processor.get_statistics()
            print(f"\n📊 ESTADÍSTICAS DETALLADAS:")
            print(f"📁 Total departamentos: {stats['total_departments']}")
            print(f"📄 Total empleados: {stats['total_pages']}")

            print(f"\n📋 ESTRUCTURA POR DEPARTAMENTOS:")
            for dept, details in stats['department_details'].items():
                print(f"\n🏢 {dept} ({details['empleados']} empleados):")
                for i, (nombre, pagina) in enumerate(zip(details['nombres'], details['paginas'])):
                    if i < 5:  # Mostrar primeros 5
                        print(f"   👤 {nombre} (página {pagina})")
                    elif i == 5:
                        print(f"   ... y {len(details['nombres'])-5} empleados más")
                        break

            # === SECCIÓN DE PREVIEW Y AJUSTE MEJORADA ===
            print(f"\n" + "="*60)
            print("🎯 PREVIEW Y AJUSTE DE POSICIÓN")
            print("="*60)

            # Slider para ajustar posición Y
            y_slider = widgets.IntSlider(
                value=Config.DEFAULT_OFFSET_Y,
                min=300,
                max=800,
                step=10,
                description='Posición Y:',
                style={'description_width': 'initial'},
                continuous_update=False
            )

            # Botón para generar preview
            preview_button = widgets.Button(
                description='👁️ Ver Preview',
                button_style='info',
                layout=widgets.Layout(width='150px')
            )

            # Área de preview
            preview_output = widgets.Output()

            def on_preview_click(b):
                with preview_output:
                    clear_output(wait=True)
                    print("🔄 Generando preview...")

                    preview_bytes = processor.create_preview(y_slider.value)
                    if preview_bytes:
                        print("✅ Preview generado:")
                        display(IPImage(data=preview_bytes, width=500))
                        print(f"📍 Posición Y actual: {y_slider.value}")
                        print("💡 Ajusta el slider y haz click en 'Ver Preview' para reposicionar")
                    else:
                        print("❌ Error generando preview")

            preview_button.on_click(on_preview_click)

            # Mostrar controles de preview
            print("🎛️ Controles de posicionamiento:")
            display(widgets.HBox([y_slider, preview_button]))
            display(preview_output)

            # Generar preview inicial
            initial_preview = processor.create_preview()
            if initial_preview:
                with preview_output:
                    print("📸 Preview inicial con posición por defecto:")
                    display(IPImage(data=initial_preview, width=500))
                    print(f"📍 Posición Y: {Config.DEFAULT_OFFSET_Y}")

            print(f"\n▶️ Una vez satisfecho con la posición, continúa con el paso 4")

        else:
            print(f"❌ {message}")
            print("\n📋 Log detallado:")
            print(processor.logger.get_full_log())

    except Exception as e:
        print(f"❌ Error creando procesador: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")

else:
    print("⚠️ Ejecuta primero el paso anterior para cargar los archivos")

#@title 🚀 Procesamiento Final MEJORADO
if ('processor' in globals() and processor and
    hasattr(processor, 'department_data') and processor.department_data):

    print("🚀 SISTEMA LISTO PARA PROCESAMIENTO FINAL")
    print("="*50)

    # Mostrar resumen pre-procesamiento
    stats = processor.get_statistics()
    print(f"📊 Se procesarán:")
    print(f"   📁 {stats['total_departments']} departamentos")
    print(f"   👥 {stats['total_pages']} empleados")
    print(f"   📍 Posición Y de firma: {y_slider.value if 'y_slider' in globals() else Config.DEFAULT_OFFSET_Y}")

    # Mostrar estructura que se creará
    print(f"\n📂 ESTRUCTURA QUE SE CREARÁ:")
    for dept, count in stats['departments'].items():
        print(f"   📁 {dept}/ → {count} archivos PDF")

    # Controles finales
    final_y_display = widgets.Label(
        value=f"Posición Y actual: {y_slider.value if 'y_slider' in globals() else Config.DEFAULT_OFFSET_Y}"
    )

    # Checkbox para log detallado
    verbose_check = widgets.Checkbox(
        value=True,  # Activado por defecto para ver el progreso
        description='📋 Mostrar log detallado durante procesamiento'
    )

    # Botón de procesamiento principal
    process_button = widgets.Button(
        description='🚀 PROCESAR TODO',
        button_style='success',
        layout=widgets.Layout(width='200px', height='50px'),
        icon='rocket'
    )

    # Área de resultados
    results_output = widgets.Output()

    def on_process_click(b):
        with results_output:
            clear_output(wait=True)

            print("🚀 INICIANDO PROCESAMIENTO COMPLETO")
            print("="*50)

            start_time = time.time()
            current_y = y_slider.value if 'y_slider' in globals() else Config.DEFAULT_OFFSET_Y

            # Configurar verbosidad
            processor.logger.set_verbose(verbose_check.value)

            try:
                # Barra de progreso para procesamiento
                dept_progress = widgets.IntProgress(
                    value=0,
                    min=0,
                    max=100,
                    description='Progreso:',
                    bar_style='success',
                    style={'bar_color': '#28a745'},
                    orientation='horizontal'
                )

                dept_label = widgets.Label(value="Iniciando procesamiento...")
                progress_display = widgets.VBox([dept_progress, dept_label])
                display(progress_display)

                # Callback de progreso
                def update_dept_progress(current, total, message):
                    dept_progress.value = int((current / total) * 100)
                    dept_label.value = f"{message} ({current}/{total})"

                # Ejecutar procesamiento
                success, message = processor.process_all(current_y, update_dept_progress)

                if success:
                    print(f"\n✅ {message}")

                    # Crear ZIP
                    print("\n📦 Creando archivo ZIP organizado...")
                    zip_file = processor.create_zip()

                    # Estadísticas finales
                    end_time = time.time()
                    duration = end_time - start_time
                    final_stats = processor.get_statistics()

                    print(f"\n🎉 ¡PROCESAMIENTO COMPLETADO!")
                    print("="*50)
                    print(f"⏱️ Tiempo total: {duration:.1f} segundos")
                    print(f"📁 Departamentos creados: {len(final_stats['directory_stats'])}")
                    print(f"📄 Archivos PDF generados: {sum(final_stats['directory_stats'].values())}")
                    print(f"📦 Archivo ZIP: {zip_file}")

                    # Mostrar estructura final
                    print(f"\n📂 ESTRUCTURA FINAL CREADA:")
                    for dept, count in final_stats['directory_stats'].items():
                        print(f"   📁 {dept}/ → {count} archivos")

                    # Botón de descarga
                    print(f"\n⬇️ DESCARGA TU ARCHIVO ORGANIZADO:")
                    files.download(zip_file)

                    if verbose_check.value:
                        print(f"\n📋 LOG COMPLETO DEL PROCESAMIENTO:")
                        print("-" * 50)
                        print(processor.logger.get_full_log())

                else:
                    print(f"❌ {message}")
                    print(f"\n📋 Log de errores:")
                    print(processor.logger.get_full_log())

            except Exception as e:
                print(f"💥 ERROR INESPERADO: {e}")
                import traceback
                print(f"\n🔍 Traceback completo:")
                print(traceback.format_exc())

    process_button.on_click(on_process_click)

    # Mostrar interfaz final
    print(f"\n⚙️ CONFIGURACIÓN FINAL:")
    display(final_y_display)
    display(verbose_check)
    print()
    display(process_button)
    display(results_output)

else:
    print("⚠️ Ejecuta primero el paso 3 para analizar el PDF")
    print("\n🔍 Estado actual:")
    if 'processor' not in globals():
        print("❌ Procesador no inicializado")
    elif not hasattr(processor, 'department_data'):
        print("❌ No hay datos de departamentos")
    elif not processor.department_data:
        print("❌ No se encontraron departamentos válidos")
    else:
        print("✅ Estado parece correcto, reintenta ejecutar esta celda")

class DirectoryManager:
    """Gestor de directorios y organización de archivos NUEVO"""
    
    def __init__(self, base_output_dir: str = "liquidaciones_firmadas"):
        self.base_output_dir = Path(base_output_dir)
        self.department_structure = {}
        self.created_dirs = set()
        
    def setup_base_directory(self):
        """Configura el directorio base"""
        if self.base_output_dir.exists():
            shutil.rmtree(self.base_output_dir)
        self.base_output_dir.mkdir(exist_ok=True)
        
    def create_department_directory(self, department_name: str) -> Path:
        """Crea directorio para un departamento específico"""
        safe_dept_name = self._sanitize_directory_name(department_name)
        dept_dir = self.base_output_dir / safe_dept_name
        
        if safe_dept_name not in self.created_dirs:
            dept_dir.mkdir(exist_ok=True)
            self.created_dirs.add(safe_dept_name)
            
        return dept_dir
    
    def _sanitize_directory_name(self, name: str) -> str:
        """Sanitiza nombres para directorios"""
        # Remover caracteres problemáticos para sistemas de archivos
        sanitized = re.sub(r'[<>:"/\\|?*]', '_', name)
        sanitized = re.sub(r'_{2,}', '_', sanitized)
        sanitized = sanitized.strip('_.')
        
        # Limitar longitud
        if len(sanitized) > 50:
            sanitized = sanitized[:50].rstrip('_')
            
        return sanitized or "DEPARTAMENTO_INVALIDO"
    
    def generate_employee_filename(self, employee_name: str, page_num: int = None) -> str:
        """Genera nombre de archivo para empleado"""
        safe_name = self._sanitize_directory_name(employee_name)
        
        if page_num:
            return f"{safe_name}_P{page_num:03d}_LIQUIDACION.pdf"
        else:
            return f"{safe_name}_LIQUIDACION.pdf"
    
    def get_directory_statistics(self) -> Dict[str, int]:
        """Obtiene estadísticas de directorios creados"""
        stats = {}
        for dept_name in self.created_dirs:
            dept_dir = self.base_output_dir / dept_name
            if dept_dir.exists():
                pdf_count = len(list(dept_dir.glob("*.pdf")))
                stats[dept_name] = pdf_count
        return stats

class SignaturePlacer:
    """Colocador inteligente de firmas MEJORADO"""

    def __init__(self, signature_path: str, width: int = None, height: int = None):
        self.signature_path = Path(signature_path)
        self.width = width or Config.DEFAULT_WIDTH
        self.height = height or Config.DEFAULT_HEIGHT

        # Validar firma
        if not self.signature_path.exists():
            raise FileNotFoundError(f"Firma no encontrada: {signature_path}")

        self._validate_signature()

    def _validate_signature(self):
        """Valida que la firma sea apropiada"""
        try:
            with Image.open(self.signature_path) as img:
                if img.mode != 'RGBA':
                    raise ValueError("La firma debe tener transparencia (formato RGBA)")

                # Verificar transparencia real
                alpha = img.split()[-1]
                if min(alpha.getdata()) == 255:
                    raise ValueError("La firma no tiene píxeles transparentes")

        except Exception as e:
            raise ValueError(f"Error validando firma: {e}")

    def find_position(self, page: fitz.Page, offset_y: float = None) -> Tuple[float, float, bool]:
        """Encuentra la mejor posición para colocar la firma MEJORADO"""

        offset_y = offset_y if offset_y is not None else Config.DEFAULT_OFFSET_Y

        # Buscar textos objetivo con prioridad
        for target_text in Config.TARGET_TEXTS:
            results = page.search_for(target_text)
            if results:
                rect = results[0]
                # Posición más inteligente
                x = rect.x0 + (rect.x1 - rect.x0) / 2 - self.width / 2
                y = rect.y1 + 15  # Offset desde el texto
                
                # Validar que esté dentro de la página
                if x < 0:
                    x = 10
                if x + self.width > page.rect.width:
                    x = page.rect.width - self.width - 10
                    
                return x, y, False

        # Posición de respaldo mejorada
        x = (page.rect.width - self.width) / 2  # Centrado horizontalmente
        y = min(offset_y, page.rect.height - self.height - 20)  # Evitar overflow
        return x, y, True

    def apply_signature(self, page: fitz.Page, x: float, y: float):
        """Aplica la firma en la posición especificada"""

        with open(self.signature_path, "rb") as sig_file:
            signature_bytes = sig_file.read()

        signature_rect = fitz.Rect(x, y, x + self.width, y + self.height)
        page.insert_image(signature_rect, stream=signature_bytes)

        return signature_rect

class PDFProcessor:
    """Procesador principal de PDF COMPLETAMENTE MEJORADO"""

    def __init__(self, pdf_path: str, signature_path: str):
        self.pdf_path = Path(pdf_path)
        self.signature_path = Path(signature_path)
        self.logger = Logger()

        # Validaciones iniciales
        if not self.pdf_path.exists():
            raise FileNotFoundError(f"PDF no encontrado: {pdf_path}")

        self.signer = SignaturePlacer(signature_path)
        self.department_data = defaultdict(list)
        
        # NUEVO: Gestor de directorios
        self.directory_manager = DirectoryManager()
        self.directory_manager.setup_base_directory()

    def analyze_pdf(self, progress_callback=None) -> Tuple[bool, str]:
        """Analiza el PDF y extrae información de departamentos MEJORADO"""

        self.logger.set_operation("Analizando estructura del PDF")
        self.department_data.clear()

        try:
            with pdfplumber.open(self.pdf_path) as pdf:
                total_pages = len(pdf.pages)
                self.logger.info(f"PDF tiene {total_pages} páginas", True)

                valid_pages = 0
                processed_pages = 0

                # Procesar páginas con análisis mejorado
                for i, page in enumerate(tqdm(pdf.pages, desc="Analizando páginas")):

                    if progress_callback:
                        progress_callback(i + 1, total_pages, f"Página {i + 1}")

                    page_num = i + 1
                    processed_pages += 1

                    try:
                        # Extraer texto con configuración mejorada
                        page_text = page.extract_text(
                            x_tolerance=2, 
                            y_tolerance=3,
                            layout=True,
                            x_density=7.25,
                            y_density=13
                        ) or ""

                        if len(page_text.strip()) < 30:
                            self.logger.warning(f"Página {page_num}: Texto insuficiente ({len(page_text)} chars)")
                            continue

                        # Análisis MEJORADO de la página
                        page_info = TextProcessor.extract_page_info(page_text)

                        self.logger.debug(f"P{page_num} - Raw Name: '{page_info['raw_name']}'")
                        self.logger.debug(f"P{page_num} - Raw Dept: '{page_info['raw_dept']}'")
                        self.logger.debug(f"P{page_num} - Score: {page_info['total_score']}")

                        if not page_info["is_valid"]:
                            self.logger.warning(f"Página {page_num}: No parece liquidación (score: {page_info['total_score']})")
                            continue

                        # Validación adicional de nombres válidos
                        if (page_info["employee_name"] in ["SIN_NOMBRE", "NOMBRE_CORTO", "NOMBRE_INVALIDO"] or
                            page_info["department"] in ["SIN_DEPARTAMENTO", "DEPARTAMENTO_CORTO", "DEPARTAMENTO_INVALIDO"]):
                            self.logger.warning(f"Página {page_num}: Información incompleta")
                            continue

                        valid_pages += 1
                        
                        # Almacenar información MEJORADA
                        page_data = {
                            "page_num": page_num,
                            "employee_name": page_info["employee_name"],
                            "raw_name": page_info["raw_name"],
                            "raw_dept": page_info["raw_dept"],
                            "keyword_matches": page_info["keyword_matches"],
                            "total_score": page_info["total_score"]
                        }
                        
                        self.department_data[page_info["department"]].append(page_data)

                        self.logger.info(
                            f"P{page_num}: '{page_info['raw_name']}' → {page_info['department']} (score: {page_info['total_score']})"
                        )

                    except Exception as e:
                        self.logger.error(f"Error en página {page_num}: {e}")
                        continue

                self.logger.success(f"Análisis completado: {valid_pages}/{processed_pages} páginas válidas, {len(self.department_data)} departamentos")
                
                # Mostrar resumen detallado
                self.logger.info("=== RESUMEN DETALLADO ===", True)
                for dept, pages in self.department_data.items():
                    self.logger.info(f"📁 {dept}: {len(pages)} empleados", True)
                    for page_data in pages[:3]:  # Mostrar primeros 3
                        self.logger.info(f"   👤 {page_data['raw_name']}", True)
                    if len(pages) > 3:
                        self.logger.info(f"   ... y {len(pages)-3} más", True)

                return True, f"✅ {valid_pages} páginas procesadas en {len(self.department_data)} departamentos"

        except Exception as e:
            error_msg = f"Error fatal en análisis: {e}"
            self.logger.error(error_msg)
            return False, error_msg

    def create_preview(self, offset_y: float = None) -> Optional[bytes]:
        """Crea una preview de cómo quedará la firma MEJORADO"""

        offset_y = offset_y if offset_y is not None else Config.DEFAULT_OFFSET_Y

        try:
            # Tomar la primera página válida para preview
            with fitz.open(self.pdf_path) as pdf:
                if not self.department_data:
                    return None

                # Obtener primera página de datos
                first_dept = next(iter(self.department_data.values()))
                first_page_num = first_dept[0]["page_num"] - 1

                page = pdf.load_page(first_page_num)

                # Crear copia para preview
                preview_pdf = fitz.open()
                preview_page = preview_pdf.new_page(width=page.rect.width, height=page.rect.height)
                preview_page.show_pdf_page(preview_page.rect, pdf, first_page_num)

                # Aplicar firma
                x, y, is_fallback = self.signer.find_position(preview_page, offset_y)
                self.signer.apply_signature(preview_page, x, y)

                # Convertir a imagen con mejor calidad
                pix = preview_page.get_pixmap(matrix=fitz.Matrix(2.0, 2.0))
                img_bytes = pix.tobytes("png")

                preview_pdf.close()
                return img_bytes

        except Exception as e:
            self.logger.error(f"Error creando preview: {e}")
            return None

    def process_all(self, offset_y: float = None, progress_callback=None) -> Tuple[bool, str]:
        """Procesa todo el PDF aplicando firmas COMPLETAMENTE MEJORADO"""

        offset_y = offset_y if offset_y is not None else Config.DEFAULT_OFFSET_Y

        self.logger.set_operation("Procesando y firmando documentos por departamento")

        try:
            with fitz.open(self.pdf_path) as original_pdf:

                total_departments = len(self.department_data)
                processed_files = 0
                failed_files = 0

                for dept_idx, (dept_name, pages_info) in enumerate(self.department_data.items()):

                    if progress_callback:
                        progress_callback(dept_idx + 1, total_departments, f"Procesando {dept_name}")

                    self.logger.info(f"📁 Procesando departamento: {dept_name} ({len(pages_info)} empleados)", True)

                    # CREAR DIRECTORIO DEL DEPARTAMENTO
                    dept_dir = self.directory_manager.create_department_directory(dept_name)
                    self.logger.info(f"📂 Directorio creado: {dept_dir}")

                    # Procesar cada empleado individualmente
                    for emp_idx, page_info in enumerate(pages_info):
                        try:
                            page_num = page_info["page_num"]
                            employee_name = page_info["employee_name"]
                            raw_name = page_info.get("raw_name", employee_name)

                            self.logger.info(f"  👤 Procesando: {raw_name} (página {page_num})")

                            # Crear PDF individual
                            new_pdf = fitz.open()
                            original_page = original_pdf.load_page(page_num - 1)
                            new_page = new_pdf.new_page(width=original_page.rect.width, height=original_page.rect.height)
                            new_page.show_pdf_page(new_page.rect, original_pdf, original_page.number)

                            # Aplicar firma
                            x, y, is_fallback = self.signer.find_position(new_page, offset_y)
                            signature_rect = self.signer.apply_signature(new_page, x, y)

                            # GENERAR NOMBRE DE ARCHIVO ÚNICO
                            filename = self.directory_manager.generate_employee_filename(employee_name, page_num)
                            output_path = dept_dir / filename

                            # Guardar archivo individual
                            new_pdf.save(str(output_path), garbage=4, deflate=True)
                            new_pdf.close()

                            processed_files += 1
                            self.logger.success(f"     ✓ Guardado: {filename}")

                        except Exception as e:
                            failed_files += 1
                            self.logger.error(f"     ✗ Error procesando {raw_name}: {e}")
                            if 'new_pdf' in locals():
                                new_pdf.close()
                            continue

                    self.logger.success(f"📁 Departamento {dept_name} completado: {len(pages_info)} archivos")

                # Estadísticas finales
                success_msg = f"✅ Procesamiento completado: {processed_files} archivos exitosos, {failed_files} fallos"
                self.logger.success(success_msg)
                
                # Mostrar estructura de directorios
                dir_stats = self.directory_manager.get_directory_statistics()
                self.logger.info("=== ESTRUCTURA FINAL ===", True)
                for dept, count in dir_stats.items():
                    self.logger.info(f"📁 {dept}: {count} archivos", True)

                return True, success_msg

        except Exception as e:
            error_msg = f"Error fatal en procesamiento: {e}"
            self.logger.error(error_msg)
            return False, error_msg

    def create_zip(self) -> str:
        """Crea archivo ZIP con todos los documentos organizados MEJORADO"""

        zip_path = "liquidaciones_firmadas_organizadas.zip"

        if Path(zip_path).exists():
            Path(zip_path).unlink()

        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED, compresslevel=6) as zf:
            
            base_dir = self.directory_manager.base_output_dir
            
            for file_path in base_dir.rglob("*.pdf"):
                # Mantener estructura de directorios en el ZIP
                arc_path = file_path.relative_to(base_dir)
                zf.write(file_path, arc_path)
                self.logger.debug(f"Añadido al ZIP: {arc_path}")

        file_size = Path(zip_path).stat().st_size / (1024*1024)  # MB
        self.logger.success(f"ZIP creado: {zip_path} ({file_size:.1f} MB)")
        return zip_path

    def get_statistics(self) -> Dict[str, any]:
        """Obtiene estadísticas DETALLADAS del procesamiento"""

        total_pages = sum(len(pages) for pages in self.department_data.values())
        
        # Estadísticas por departamento
        dept_details = {}
        for dept, pages in self.department_data.items():
            dept_details[dept] = {
                "empleados": len(pages),
                "nombres": [page["raw_name"] for page in pages],
                "paginas": [page["page_num"] for page in pages]
            }

        stats = {
            "total_departments": len(self.department_data),
            "total_pages": total_pages,
            "departments": {dept: len(pages) for dept, pages in self.department_data.items()},
            "department_details": dept_details,
            "directory_stats": self.directory_manager.get_directory_statistics() if hasattr(self, 'directory_manager') else {}
        }

        return stats

print("✅ Clases core MEJORADAS configuradas correctamente")
print("🆕 Nuevas características:")
print("   📁 Gestión automática de directorios por departamento")
print("   👤 Archivos individuales por empleado")
print("   🔍 Extracción mejorada de nombres y departamentos")
print("   📊 Estadísticas detalladas")

#@title 📤 Carga de Archivos
class FileManager:
    """Gestor de archivos optimizado"""

    @staticmethod
    def setup_environment():
        """Limpia el entorno de trabajo"""

        # Limpiar directorio de salida
        output_dir = Path("liquidaciones_firmadas")
        if output_dir.exists():
            shutil.rmtree(output_dir)

        # Limpiar archivos temporales
        for pattern in ["*.pdf", "*.zip"]:
            for file in Path(".").glob(pattern):
                try:
                    file.unlink()
                except:
                    pass

        print("🧹 Entorno limpiado")

    @staticmethod
    def load_pdf() -> Tuple[Optional[str], str]:
        """Carga archivo PDF con validación"""

        print("📤 Selecciona el archivo PDF con las liquidaciones:")
        uploaded = files.upload()

        if not uploaded:
            return None, "❌ No se seleccionó ningún archivo"

        pdf_path = list(uploaded.keys())[0]

        # Validaciones
        if not pdf_path.lower().endswith('.pdf'):
            return None, "❌ El archivo debe ser un PDF"

        try:
            with pdfplumber.open(pdf_path) as pdf:
                page_count = len(pdf.pages)

            print(f"✅ PDF cargado: '{pdf_path}' ({page_count} páginas)")
            return pdf_path, f"✅ {page_count} páginas cargadas"

        except Exception as e:
            return None, f"❌ Error al abrir PDF: {e}"

    @staticmethod
    def load_signature() -> Tuple[Optional[str], str]:
        """Carga archivo de firma con validación avanzada"""

        print("📤 Selecciona la firma PNG (debe tener fondo transparente):")
        uploaded = files.upload()

        if not uploaded:
            return None, "❌ No se seleccionó ninguna firma"

        sig_path = list(uploaded.keys())[0]

        # Validaciones
        if not sig_path.lower().endswith('.png'):
            return None, "❌ La firma debe ser PNG"

        try:
            with Image.open(sig_path) as img:
                if img.mode != 'RGBA':
                    return None, "❌ La firma debe tener transparencia (RGBA)"

                # Verificar transparencia real
                alpha = img.split()[-1]
                if min(alpha.getdata()) == 255:
                    return None, "❌ La firma no tiene píxeles transparentes"

                width, height = img.size

            print(f"✅ Firma cargada: '{sig_path}' ({width}x{height}px)")
            return sig_path, f"✅ Firma válida ({width}x{height}px)"

        except Exception as e:
            return None, f"❌ Error validando firma: {e}"

# Ejecutar configuración inicial
FileManager.setup_environment()

# Cargar archivos
pdf_path, pdf_status = FileManager.load_pdf()
if pdf_path:
    print(pdf_status)
    signature_path, sig_status = FileManager.load_signature()
    if signature_path:
        print(sig_status)
        print("\n🎯 Archivos cargados correctamente. Continúa con el siguiente paso.")
    else:
        print(f"Error con firma: {sig_status}")
else:
    print(f"Error con PDF: {pdf_status}")

#@title 🔍 Análisis y Preview del PDF MEJORADO
if 'pdf_path' in globals() and pdf_path and 'signature_path' in globals() and signature_path:

    print("🔍 Iniciando análisis MEJORADO del PDF...")

    # Crear procesador
    try:
        processor = PDFProcessor(pdf_path, signature_path)
        processor.logger.set_verbose(True)  # Más detallado para análisis

        # Widget de progreso
        progress_bar = widgets.IntProgress(
            value=0,
            min=0,
            max=100,
            description='Analizando:',
            bar_style='info',
            style={'bar_color': '#20B2AA'},
            orientation='horizontal'
        )

        progress_label = widgets.Label(value="Iniciando análisis...")

        display(widgets.VBox([progress_bar, progress_label]))

        # Función de callback para progreso
        def update_progress(current, total, message):
            progress_bar.value = int((current / total) * 100)
            progress_label.value = f"{message} ({current}/{total})"

        # Ejecutar análisis
        success, message = processor.analyze_pdf(update_progress)

        if success:
            print(f"\n{message}")

            # Mostrar estadísticas DETALLADAS
            stats = processor.get_statistics()
            print(f"\n📊 ESTADÍSTICAS DETALLADAS:")
            print(f"📁 Total departamentos: {stats['total_departments']}")
            print(f"📄 Total empleados: {stats['total_pages']}")

            print(f"\n📋 ESTRUCTURA POR DEPARTAMENTOS:")
            for dept, details in stats['department_details'].items():
                print(f"\n🏢 {dept} ({details['empleados']} empleados):")
                for i, (nombre, pagina) in enumerate(zip(details['nombres'], details['paginas'])):
                    if i < 5:  # Mostrar primeros 5
                        print(f"   👤 {nombre} (página {pagina})")
                    elif i == 5:
                        print(f"   ... y {len(details['nombres'])-5} empleados más")
                        break

            # === SECCIÓN DE PREVIEW Y AJUSTE MEJORADA ===
            print(f"\n" + "="*60)
            print("🎯 PREVIEW Y AJUSTE DE POSICIÓN")
            print("="*60)

            # Slider para ajustar posición Y
            y_slider = widgets.IntSlider(
                value=Config.DEFAULT_OFFSET_Y,
                min=300,
                max=800,
                step=10,
                description='Posición Y:',
                style={'description_width': 'initial'},
                continuous_update=False
            )

            # Botón para generar preview
            preview_button = widgets.Button(
                description='👁️ Ver Preview',
                button_style='info',
                layout=widgets.Layout(width='150px')
            )

            # Área de preview
            preview_output = widgets.Output()

            def on_preview_click(b):
                with preview_output:
                    clear_output(wait=True)
                    print("🔄 Generando preview...")

                    preview_bytes = processor.create_preview(y_slider.value)
                    if preview_bytes:
                        print("✅ Preview generado:")
                        display(IPImage(data=preview_bytes, width=500))
                        print(f"📍 Posición Y actual: {y_slider.value}")
                        print("💡 Ajusta el slider y haz click en 'Ver Preview' para reposicionar")
                    else:
                        print("❌ Error generando preview")

            preview_button.on_click(on_preview_click)

            # Mostrar controles de preview
            print("🎛️ Controles de posicionamiento:")
            display(widgets.HBox([y_slider, preview_button]))
            display(preview_output)

            # Generar preview inicial
            initial_preview = processor.create_preview()
            if initial_preview:
                with preview_output:
                    print("📸 Preview inicial con posición por defecto:")
                    display(IPImage(data=initial_preview, width=500))
                    print(f"📍 Posición Y: {Config.DEFAULT_OFFSET_Y}")

            print(f"\n▶️ Una vez satisfecho con la posición, continúa con el paso 4")

        else:
            print(f"❌ {message}")
            print("\n📋 Log detallado:")
            print(processor.logger.get_full_log())

    except Exception as e:
        print(f"❌ Error creando procesador: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")

else:
    print("⚠️ Ejecuta primero el paso anterior para cargar los archivos")

#@title 🚀 Procesamiento Final MEJORADO
if ('processor' in globals() and processor and
    hasattr(processor, 'department_data') and processor.department_data):

    print("🚀 SISTEMA LISTO PARA PROCESAMIENTO FINAL")
    print("="*50)

    # Mostrar resumen pre-procesamiento
    stats = processor.get_statistics()
    print(f"📊 Se procesarán:")
    print(f"   📁 {stats['total_departments']} departamentos")
    print(f"   👥 {stats['total_pages']} empleados")
    print(f"   📍 Posición Y de firma: {y_slider.value if 'y_slider' in globals() else Config.DEFAULT_OFFSET_Y}")

    # Mostrar estructura que se creará
    print(f"\n📂 ESTRUCTURA QUE SE CREARÁ:")
    for dept, count in stats['departments'].items():
        print(f"   📁 {dept}/ → {count} archivos PDF")

    # Controles finales
    final_y_display = widgets.Label(
        value=f"Posición Y actual: {y_slider.value if 'y_slider' in globals() else Config.DEFAULT_OFFSET_Y}"
    )

    # Checkbox para log detallado
    verbose_check = widgets.Checkbox(
        value=True,  # Activado por defecto para ver el progreso
        description='📋 Mostrar log detallado durante procesamiento'
    )

    # Botón de procesamiento principal
    process_button = widgets.Button(
        description='🚀 PROCESAR TODO',
        button_style='success',
        layout=widgets.Layout(width='200px', height='50px'),
        icon='rocket'
    )

    # Área de resultados
    results_output = widgets.Output()

    def on_process_click(b):
        with results_output:
            clear_output(wait=True)

            print("🚀 INICIANDO PROCESAMIENTO COMPLETO")
            print("="*50)

            start_time = time.time()
            current_y = y_slider.value if 'y_slider' in globals() else Config.DEFAULT_OFFSET_Y

            # Configurar verbosidad
            processor.logger.set_verbose(verbose_check.value)

            try:
                # Barra de progreso para procesamiento
                dept_progress = widgets.IntProgress(
                    value=0,
                    min=0,
                    max=100,
                    description='Progreso:',
                    bar_style='success',
                    style={'bar_color': '#28a745'},
                    orientation='horizontal'
                )

                dept_label = widgets.Label(value="Iniciando procesamiento...")
                progress_display = widgets.VBox([dept_progress, dept_label])
                display(progress_display)

                # Callback de progreso
                def update_dept_progress(current, total, message):
                    dept_progress.value = int((current / total) * 100)
                    dept_label.value = f"{message} ({current}/{total})"

                # Ejecutar procesamiento
                success, message = processor.process_all(current_y, update_dept_progress)

                if success:
                    print(f"\n✅ {message}")

                    # Crear ZIP
                    print("\n📦 Creando archivo ZIP organizado...")
                    zip_file = processor.create_zip()

                    # Estadísticas finales
                    end_time = time.time()
                    duration = end_time - start_time
                    final_stats = processor.get_statistics()

                    print(f"\n🎉 ¡PROCESAMIENTO COMPLETADO!")
                    print("="*50)
                    print(f"⏱️ Tiempo total: {duration:.1f} segundos")
                    print(f"📁 Departamentos creados: {len(final_stats['directory_stats'])}")
                    print(f"📄 Archivos PDF generados: {sum(final_stats['directory_stats'].values())}")
                    print(f"📦 Archivo ZIP: {zip_file}")

                    # Mostrar estructura final
                    print(f"\n📂 ESTRUCTURA FINAL CREADA:")
                    for dept, count in final_stats['directory_stats'].items():
                        print(f"   📁 {dept}/ → {count} archivos")

                    # Botón de descarga
                    print(f"\n⬇️ DESCARGA TU ARCHIVO ORGANIZADO:")
                    files.download(zip_file)

                    if verbose_check.value:
                        print(f"\n📋 LOG COMPLETO DEL PROCESAMIENTO:")
                        print("-" * 50)
                        print(processor.logger.get_full_log())

                else:
                    print(f"❌ {message}")
                    print(f"\n📋 Log de errores:")
                    print(processor.logger.get_full_log())

            except Exception as e:
                print(f"💥 ERROR INESPERADO: {e}")
                import traceback
                print(f"\n🔍 Traceback completo:")
                print(traceback.format_exc())

    process_button.on_click(on_process_click)

    # Mostrar interfaz final
    print(f"\n⚙️ CONFIGURACIÓN FINAL:")
    display(final_y_display)
    display(verbose_check)
    print()
    display(process_button)
    display(results_output)

else:
    print("⚠️ Ejecuta primero el paso 3 para analizar el PDF")
    print("\n🔍 Estado actual:")
    if 'processor' not in globals():
        print("❌ Procesador no inicializado")
    elif not hasattr(processor, 'department_data'):
        print("❌ No hay datos de departamentos")
    elif not processor.department_data:
        print("❌ No se encontraron departamentos válidos")
    else:
        print("✅ Estado parece correcto, reintenta ejecutar esta celda")
            # Buscar palabras clave que indiquen el tipo de institución
            if any(word in normalized_text.upper() for word in ['EDUCACION', 'EDUCACIÓN', 'ESCUELA', 'LICEO', 'COLEGIO']):
                department = "DEPARTAMENTO_EDUCACION"
                raw_dept_found = "EDUCACION (detectado por contenido)"
            elif any(word in normalized_text.upper() for word in ['MUNICIPAL', 'MUNICIPALIDAD']):
                department = "ADMINISTRACION_MUNICIPAL"
                raw_dept_found = "MUNICIPAL (detectado por contenido)"
            else:
                department = "DEPARTAMENTO_GENERAL"
                raw_dept_found = "GENERAL (sin departamento específico)"

        # Validar que es una liquidación (criteria MÁS PERMISIVO)
        keyword_count = sum(1 for keyword in Config.VALIDATION_KEYWORDS
                          if keyword.lower() in page_text.lower())
        
        # También buscar palabras clave adicionales
        additional_keywords = ["remuneracion", "sueldo", "salario", "pago", "trabajador", "empleado", "haberes"]
        additional_count = sum(1 for keyword in additional_keywords
                             if keyword.lower() in page_text.lower())
        
        # VALIDACIÓN MÁS PERMISIVA: Si tiene nombre válido, aceptar
        is_valid = (keyword_count >= 1 or additional_count >= 1 or 
                   (employee_name != "SIN_NOMBRE" and len(raw_name_found) > 5))

        return {
            "employee_name": employee_name,
            "department": department,
            "is_valid": is_valid,
            "keyword_matches": keyword_count,
            "additional_matches": additional_count,
            "raw_name": raw_name_found,
            "raw_dept": raw_dept_found,
            "total_score": keyword_count + additional_count,
            "debug_lines": lines[:5]  # Para debugging
        }

class DirectoryManager:
    """Gestor de directorios y organización de archivos NUEVO"""
    
    def __init__(self, base_output_dir: str = "liquidaciones_firmadas"):
        self.base_output_dir = Path(base_output_dir)
        self.department_structure = {}
        self.created_dirs = set()
        
    def setup_base_directory(self):
        """Configura el directorio base"""
        if self.base_output_dir.exists():
            shutil.rmtree(self.base_output_dir)
        self.base_output_dir.mkdir(exist_ok=True)
        
    def create_department_directory(self, department_name: str) -> Path:
        """Crea directorio para un departamento específico"""
        safe_dept_name = self._sanitize_directory_name(department_name)
        dept_dir = self.base_output_dir / safe_dept_name
        
        if safe_dept_name not in self.created_dirs:
            dept_dir.mkdir(exist_ok=True)
            self.created_dirs.add(safe_dept_name)
            
        return dept_dir
    
    def _sanitize_directory_name(self, name: str) -> str:
        """Sanitiza nombres para directorios"""
        # Remover caracteres problemáticos para sistemas de archivos
        sanitized = re.sub(r'[<>:"/\\|?*]', '_', name)
        sanitized = re.sub(r'_{2,}', '_', sanitized)
        sanitized = sanitized.strip('_.')
        
        # Limitar longitud
        if len(sanitized) > 50:
            sanitized = sanitized[:50].rstrip('_')
            
        return sanitized or "DEPARTAMENTO_INVALIDO"
    
    def generate_employee_filename(self, employee_name: str, page_num: int = None) -> str:
        """Genera nombre de archivo para empleado"""
        safe_name = self._sanitize_directory_name(employee_name)
        
        if page_num:
            return f"{safe_name}_P{page_num:03d}_LIQUIDACION.pdf"
        else:
            return f"{safe_name}_LIQUIDACION.pdf"
    
    def get_directory_statistics(self) -> Dict[str, int]:
        """Obtiene estadísticas de directorios creados"""
        stats = {}
        for dept_name in self.created_dirs:
            dept_dir = self.base_output_dir / dept_name
            if dept_dir.exists():
                pdf_count = len(list(dept_dir.glob("*.pdf")))
                stats[dept_name] = pdf_count
        return stats

class SignaturePlacer:
    """Colocador inteligente de firmas MEJORADO"""

    def __init__(self, signature_path: str, width: int = None, height: int = None):
        self.signature_path = Path(signature_path)
        self.width = width or Config.DEFAULT_WIDTH
        self.height = height or Config.DEFAULT_HEIGHT

        # Validar firma
        if not self.signature_path.exists():
            raise FileNotFoundError(f"Firma no encontrada: {signature_path}")

        self._validate_signature()

    def _validate_signature(self):
        """Valida que la firma sea apropiada"""
        try:
            with Image.open(self.signature_path) as img:
                if img.mode != 'RGBA':
                    raise ValueError("La firma debe tener transparencia (formato RGBA)")

                # Verificar transparencia real
                alpha = img.split()[-1]
                if min(alpha.getdata()) == 255:
                    raise ValueError("La firma no tiene píxeles transparentes")

        except Exception as e:
            raise ValueError(f"Error validando firma: {e}")

    def find_position(self, page: fitz.Page, offset_y: float = None) -> Tuple[float, float, bool]:
        """Encuentra la mejor posición para colocar la firma MEJORADO"""

        offset_y = offset_y if offset_y is not None else Config.DEFAULT_OFFSET_Y

        # Buscar textos objetivo con prioridad
        for target_text in Config.TARGET_TEXTS:
            results = page.search_for(target_text)
            if results:
                rect = results[0]
                # Posición más inteligente
                x = rect.x0 + (rect.x1 - rect.x0) / 2 - self.width / 2
                y = rect.y1 + 15  # Offset desde el texto
                
                # Validar que esté dentro de la página
                if x < 0:
                    x = 10
                if x + self.width > page.rect.width:
                    x = page.rect.width - self.width - 10
                    
                return x, y, False

        # Posición de respaldo mejorada
        x = (page.rect.width - self.width) / 2  # Centrado horizontalmente
        y = min(offset_y, page.rect.height - self.height - 20)  # Evitar overflow
        return x, y, True

    def apply_signature(self, page: fitz.Page, x: float, y: float):
        """Aplica la firma en la posición especificada"""

        with open(self.signature_path, "rb") as sig_file:
            signature_bytes = sig_file.read()

        signature_rect = fitz.Rect(x, y, x + self.width, y + self.height)
        page.insert_image(signature_rect, stream=signature_bytes)

        return signature_rect

class PDFProcessor:
    """Procesador principal de PDF COMPLETAMENTE MEJORADO"""

    def __init__(self, pdf_path: str, signature_path: str):
        self.pdf_path = Path(pdf_path)
        self.signature_path = Path(signature_path)
        self.logger = Logger()

        # Validaciones iniciales
        if not self.pdf_path.exists():
            raise FileNotFoundError(f"PDF no encontrado: {pdf_path}")

        self.signer = SignaturePlacer(signature_path)
        self.department_data = defaultdict(list)
        
        # NUEVO: Gestor de directorios
        self.directory_manager = DirectoryManager()
        self.directory_manager.setup_base_directory()

    def analyze_pdf(self, progress_callback=None) -> Tuple[bool, str]:
        """Analiza el PDF y extrae información de departamentos MEJORADO"""

        self.logger.set_operation("Analizando estructura del PDF")
        self.department_data.clear()

        try:
            with pdfplumber.open(self.pdf_path) as pdf:
                total_pages = len(pdf.pages)
                self.logger.info(f"PDF tiene {total_pages} páginas", True)

                valid_pages = 0
                processed_pages = 0

                # Procesar páginas con análisis mejorado
                for i, page in enumerate(tqdm(pdf.pages, desc="Analizando páginas")):

                    if progress_callback:
                        progress_callback(i + 1, total_pages, f"Página {i + 1}")

                    page_num = i + 1
                    processed_pages += 1

                    try:
                        # Extraer texto con configuración mejorada
                        page_text = page.extract_text(
                            x_tolerance=2, 
                            y_tolerance=3,
                            layout=True,
                            x_density=7.25,
                            y_density=13
                        ) or ""

                        if len(page_text.strip()) < 30:
                            self.logger.warning(f"Página {page_num}: Texto insuficiente ({len(page_text)} chars)")
                            continue

                        # Análisis MEJORADO de la página
                        page_info = TextProcessor.extract_page_info(page_text)

                        self.logger.debug(f"P{page_num} - Raw Name: '{page_info['raw_name']}'")
                        self.logger.debug(f"P{page_num} - Raw Dept: '{page_info['raw_dept']}'")
                        self.logger.debug(f"P{page_num} - Score: {page_info['total_score']}")

                        if not page_info["is_valid"]:
                            self.logger.warning(f"Página {page_num}: No parece liquidación (score: {page_info['total_score']})")
                            continue

                        # Validación adicional de nombres válidos
                        if (page_info["employee_name"] in ["SIN_NOMBRE", "NOMBRE_CORTO", "NOMBRE_INVALIDO"] or
                            page_info["department"] in ["SIN_DEPARTAMENTO", "DEPARTAMENTO_CORTO", "DEPARTAMENTO_INVALIDO"]):
                            self.logger.warning(f"Página {page_num}: Información incompleta")
                            continue

                        valid_pages += 1
                        
                        # Almacenar información MEJORADA
                        page_data = {
                            "page_num": page_num,
                            "employee_name": page_info["employee_name"],
                            "raw_name": page_info["raw_name"],
                            "raw_dept": page_info["raw_dept"],
                            "keyword_matches": page_info["keyword_matches"],
                            "total_score": page_info["total_score"]
                        }
                        
                        self.department_data[page_info["department"]].append(page_data)

                        self.logger.info(
                            f"P{page_num}: '{page_info['raw_name']}' → {page_info['department']} (score: {page_info['total_score']})"
                        )

                    except Exception as e:
                        self.logger.error(f"Error en página {page_num}: {e}")
                        continue

                self.logger.success(f"Análisis completado: {valid_pages}/{processed_pages} páginas válidas, {len(self.department_data)} departamentos")
                
                # Mostrar resumen detallado
                self.logger.info("=== RESUMEN DETALLADO ===", True)
                for dept, pages in self.department_data.items():
                    self.logger.info(f"📁 {dept}: {len(pages)} empleados", True)
                    for page_data in pages[:3]:  # Mostrar primeros 3
                        self.logger.info(f"   👤 {page_data['raw_name']}", True)
                    if len(pages) > 3:
                        self.logger.info(f"   ... y {len(pages)-3} más", True)

                return True, f"✅ {valid_pages} páginas procesadas en {len(self.department_data)} departamentos"

        except Exception as e:
            error_msg = f"Error fatal en análisis: {e}"
            self.logger.error(error_msg)
            return False, error_msg

    def create_preview(self, offset_y: float = None) -> Optional[bytes]:
        """Crea una preview de cómo quedará la firma MEJORADO"""

        offset_y = offset_y if offset_y is not None else Config.DEFAULT_OFFSET_Y

        try:
            # Tomar la primera página válida para preview
            with fitz.open(self.pdf_path) as pdf:
                if not self.department_data:
                    return None

                # Obtener primera página de datos
                first_dept = next(iter(self.department_data.values()))
                first_page_num = first_dept[0]["page_num"] - 1

                page = pdf.load_page(first_page_num)

                # Crear copia para preview
                preview_pdf = fitz.open()
                preview_page = preview_pdf.new_page(width=page.rect.width, height=page.rect.height)
                preview_page.show_pdf_page(preview_page.rect, pdf, first_page_num)

                # Aplicar firma
                x, y, is_fallback = self.signer.find_position(preview_page, offset_y)
                self.signer.apply_signature(preview_page, x, y)

                # Convertir a imagen con mejor calidad
                pix = preview_page.get_pixmap(matrix=fitz.Matrix(2.0, 2.0))
                img_bytes = pix.tobytes("png")

                preview_pdf.close()
                return img_bytes

        except Exception as e:
            self.logger.error(f"Error creando preview: {e}")
            return None

    def process_all(self, offset_y: float = None, progress_callback=None) -> Tuple[bool, str]:
        """Procesa todo el PDF aplicando firmas COMPLETAMENTE MEJORADO"""

        offset_y = offset_y if offset_y is not None else Config.DEFAULT_OFFSET_Y

        self.logger.set_operation("Procesando y firmando documentos por departamento")

        try:
            with fitz.open(self.pdf_path) as original_pdf:

                total_departments = len(self.department_data)
                processed_files = 0
                failed_files = 0

                for dept_idx, (dept_name, pages_info) in enumerate(self.department_data.items()):

                    if progress_callback:
                        progress_callback(dept_idx + 1, total_departments, f"Procesando {dept_name}")

                    self.logger.info(f"📁 Procesando departamento: {dept_name} ({len(pages_info)} empleados)", True)

                    # CREAR DIRECTORIO DEL DEPARTAMENTO
                    dept_dir = self.directory_manager.create_department_directory(dept_name)
                    self.logger.info(f"📂 Directorio creado: {dept_dir}")

                    # Procesar cada empleado individualmente
                    for emp_idx, page_info in enumerate(pages_info):
                        try:
                            page_num = page_info["page_num"]
                            employee_name = page_info["employee_name"]
                            raw_name = page_info.get("raw_name", employee_name)

                            self.logger.info(f"  👤 Procesando: {raw_name} (página {page_num})")

                            # Crear PDF individual
                            new_pdf = fitz.open()
                            original_page = original_pdf.load_page(page_num - 1)
                            new_page = new_pdf.new_page(width=original_page.rect.width, height=original_page.rect.height)
                            new_page.show_pdf_page(new_page.rect, original_pdf, original_page.number)

                            # Aplicar firma
                            x, y, is_fallback = self.signer.find_position(new_page, offset_y)
                            signature_rect = self.signer.apply_signature(new_page, x, y)

                            # GENERAR NOMBRE DE ARCHIVO ÚNICO
                            filename = self.directory_manager.generate_employee_filename(employee_name, page_num)
                            output_path = dept_dir / filename

                            # Guardar archivo individual
                            new_pdf.save(str(output_path), garbage=4, deflate=True)
                            new_pdf.close()

                            processed_files += 1
                            self.logger.success(f"     ✓ Guardado: {filename}")

                        except Exception as e:
                            failed_files += 1
                            self.logger.error(f"     ✗ Error procesando {raw_name}: {e}")
                            if 'new_pdf' in locals():
                                new_pdf.close()
                            continue

                    self.logger.success(f"📁 Departamento {dept_name} completado: {len(pages_info)} archivos")

                # Estadísticas finales
                success_msg = f"✅ Procesamiento completado: {processed_files} archivos exitosos, {failed_files} fallos"
                self.logger.success(success_msg)
                
                # Mostrar estructura de directorios
                dir_stats = self.directory_manager.get_directory_statistics()
                self.logger.info("=== ESTRUCTURA FINAL ===", True)
                for dept, count in dir_stats.items():
                    self.logger.info(f"📁 {dept}: {count} archivos", True)

                return True, success_msg

        except Exception as e:
            error_msg = f"Error fatal en procesamiento: {e}"
            self.logger.error(error_msg)
            return False, error_msg

    def create_zip(self) -> str:
        """Crea archivo ZIP con todos los documentos organizados MEJORADO"""

        zip_path = "liquidaciones_firmadas_organizadas.zip"

        if Path(zip_path).exists():
            Path(zip_path).unlink()

        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED, compresslevel=6) as zf:
            
            base_dir = self.directory_manager.base_output_dir
            
            for file_path in base_dir.rglob("*.pdf"):
                # Mantener estructura de directorios en el ZIP
                arc_path = file_path.relative_to(base_dir)
                zf.write(file_path, arc_path)
                self.logger.debug(f"Añadido al ZIP: {arc_path}")

        file_size = Path(zip_path).stat().st_size / (1024*1024)  # MB
        self.logger.success(f"ZIP creado: {zip_path} ({file_size:.1f} MB)")
        return zip_path

    def get_statistics(self) -> Dict[str, any]:
        """Obtiene estadísticas DETALLADAS del procesamiento"""

        total_pages = sum(len(pages) for pages in self.department_data.values())
        
        # Estadísticas por departamento
        dept_details = {}
        for dept, pages in self.department_data.items():
            dept_details[dept] = {
                "empleados": len(pages),
                "nombres": [page["raw_name"] for page in pages],
                "paginas": [page["page_num"] for page in pages]
            }

        stats = {
            "total_departments": len(self.department_data),
            "total_pages": total_pages,
            "departments": {dept: len(pages) for dept, pages in self.department_data.items()},
            "department_details": dept_details,
            "directory_stats": self.directory_manager.get_directory_statistics() if hasattr(self, 'directory_manager') else {}
        }

        return stats

print("✅ Clases core MEJORADAS configuradas correctamente")
print("🆕 Nuevas características:")
print("   📁 Gestión automática de directorios por departamento")
print("   👤 Archivos individuales por empleado")
print("   🔍 Extracción mejorada de nombres y departamentos")
print("   📊 Estadísticas detalladas")

#@title 📤 Carga de Archivos
class FileManager:
    """Gestor de archivos optimizado"""

    @staticmethod
    def setup_environment():
        """Limpia el entorno de trabajo"""

        # Limpiar directorio de salida
        output_dir = Path("liquidaciones_firmadas")
        if output_dir.exists():
            shutil.rmtree(output_dir)

        # Limpiar archivos temporales
        for pattern in ["*.pdf", "*.zip"]:
            for file in Path(".").glob(pattern):
                try:
                    file.unlink()
                except:
                    pass

        print("🧹 Entorno limpiado")

    @staticmethod
    def load_pdf() -> Tuple[Optional[str], str]:
        """Carga archivo PDF con validación"""

        print("📤 Selecciona el archivo PDF con las liquidaciones:")
        uploaded = files.upload()

        if not uploaded:
            return None, "❌ No se seleccionó ningún archivo"

        pdf_path = list(uploaded.keys())[0]

        # Validaciones
        if not pdf_path.lower().endswith('.pdf'):
            return None, "❌ El archivo debe ser un PDF"

        try:
            with pdfplumber.open(pdf_path) as pdf:
                page_count = len(pdf.pages)

            print(f"✅ PDF cargado: '{pdf_path}' ({page_count} páginas)")
            return pdf_path, f"✅ {page_count} páginas cargadas"

        except Exception as e:
            return None, f"❌ Error al abrir PDF: {e}"

    @staticmethod
    def load_signature() -> Tuple[Optional[str], str]:
        """Carga archivo de firma con validación avanzada"""

        print("📤 Selecciona la firma PNG (debe tener fondo transparente):")
        uploaded = files.upload()

        if not uploaded:
            return None, "❌ No se seleccionó ninguna firma"

        sig_path = list(uploaded.keys())[0]

        # Validaciones
        if not sig_path.lower().endswith('.png'):
            return None, "❌ La firma debe ser PNG"

        try:
            with Image.open(sig_path) as img:
                if img.mode != 'RGBA':
                    return None, "❌ La firma debe tener transparencia (RGBA)"

                # Verificar transparencia real
                alpha = img.split()[-1]
                if min(alpha.getdata()) == 255:
                    return None, "❌ La firma no tiene píxeles transparentes"

                width, height = img.size

            print(f"✅ Firma cargada: '{sig_path}' ({width}x{height}px)")
            return sig_path, f"✅ Firma válida ({width}x{height}px)"

        except Exception as e:
            return None, f"❌ Error validando firma: {e}"

# Ejecutar configuración inicial
FileManager.setup_environment()

# Cargar archivos
pdf_path, pdf_status = FileManager.load_pdf()
if pdf_path:
    print(pdf_status)
    signature_path, sig_status = FileManager.load_signature()
    if signature_path:
        print(sig_status)
        print("\n🎯 Archivos cargados correctamente. Continúa con el siguiente paso.")
    else:
        print(f"Error con firma: {sig_status}")
else:
    print(f"Error con PDF: {pdf_status}")

#@title 🔍 Análisis y Preview del PDF MEJORADO
if 'pdf_path' in globals() and pdf_path and 'signature_path' in globals() and signature_path:

    print("🔍 Iniciando análisis MEJORADO del PDF...")

    # Crear procesador
    try:
        processor = PDFProcessor(pdf_path, signature_path)
        processor.logger.set_verbose(True)  # Más detallado para análisis

        # Widget de progreso
        progress_bar = widgets.IntProgress(
            value=0,
            min=0,
            max=100,
            description='Analizando:',
            bar_style='info',
            style={'bar_color': '#20B2AA'},
            orientation='horizontal'
        )

        progress_label = widgets.Label(value="Iniciando análisis...")

        display(widgets.VBox([progress_bar, progress_label]))

        # Función de callback para progreso
        def update_progress(current, total, message):
            progress_bar.value = int((current / total) * 100)
            progress_label.value = f"{message} ({current}/{total})"

        # Ejecutar análisis
        success, message = processor.analyze_pdf(update_progress)

        if success:
            print(f"\n{message}")

            # Mostrar estadísticas DETALLADAS
            stats = processor.get_statistics()
            print(f"\n📊 ESTADÍSTICAS DETALLADAS:")
            print(f"📁 Total departamentos: {stats['total_departments']}")
            print(f"📄 Total empleados: {stats['total_pages']}")

            print(f"\n📋 ESTRUCTURA POR DEPARTAMENTOS:")
            for dept, details in stats['department_details'].items():
                print(f"\n🏢 {dept} ({details['empleados']} empleados):")
                for i, (nombre, pagina) in enumerate(zip(details['nombres'], details['paginas'])):
                    if i < 5:  # Mostrar primeros 5
                        print(f"   👤 {nombre} (página {pagina})")
                    elif i == 5:
                        print(f"   ... y {len(details['nombres'])-5} empleados más")
                        break

            # === SECCIÓN DE PREVIEW Y AJUSTE MEJORADA ===
            print(f"\n" + "="*60)
            print("🎯 PREVIEW Y AJUSTE DE POSICIÓN")
            print("="*60)

            # Slider para ajustar posición Y
            y_slider = widgets.IntSlider(
                value=Config.DEFAULT_OFFSET_Y,
                min=300,
                max=800,
                step=10,
                description='Posición Y:',
                style={'description_width': 'initial'},
                continuous_update=False
            )

            # Botón para generar preview
            preview_button = widgets.Button(
                description='👁️ Ver Preview',
                button_style='info',
                layout=widgets.Layout(width='150px')
            )

            # Área de preview
            preview_output = widgets.Output()

            def on_preview_click(b):
                with preview_output:
                    clear_output(wait=True)
                    print("🔄 Generando preview...")

                    preview_bytes = processor.create_preview(y_slider.value)
                    if preview_bytes:
                        print("✅ Preview generado:")
                        display(IPImage(data=preview_bytes, width=500))
                        print(f"📍 Posición Y actual: {y_slider.value}")
                        print("💡 Ajusta el slider y haz click en 'Ver Preview' para reposicionar")
                    else:
                        print("❌ Error generando preview")

            preview_button.on_click(on_preview_click)

            # Mostrar controles de preview
            print("🎛️ Controles de posicionamiento:")
            display(widgets.HBox([y_slider, preview_button]))
            display(preview_output)

            # Generar preview inicial
            initial_preview = processor.create_preview()
            if initial_preview:
                with preview_output:
                    print("📸 Preview inicial con posición por defecto:")
                    display(IPImage(data=initial_preview, width=500))
                    print(f"📍 Posición Y: {Config.DEFAULT_OFFSET_Y}")

            print(f"\n▶️ Una vez satisfecho con la posición, continúa con el paso 4")

        else:
            print(f"❌ {message}")
            print("\n📋 Log detallado:")
            print(processor.logger.get_full_log())

    except Exception as e:
        print(f"❌ Error creando procesador: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")

else:
    print("⚠️ Ejecuta primero el paso anterior para cargar los archivos")

#@title 🚀 Procesamiento Final MEJORADO
if ('processor' in globals() and processor and
    hasattr(processor, 'department_data') and processor.department_data):

    print("🚀 SISTEMA LISTO PARA PROCESAMIENTO FINAL")
    print("="*50)

    # Mostrar resumen pre-procesamiento
    stats = processor.get_statistics()
    print(f"📊 Se procesarán:")
    print(f"   📁 {stats['total_departments']} departamentos")
    print(f"   👥 {stats['total_pages']} empleados")
    print(f"   📍 Posición Y de firma: {y_slider.value if 'y_slider' in globals() else Config.DEFAULT_OFFSET_Y}")

    # Mostrar estructura que se creará
    print(f"\n📂 ESTRUCTURA QUE SE CREARÁ:")
    for dept, count in stats['departments'].items():
        print(f"   📁 {dept}/ → {count} archivos PDF")

    # Controles finales
    final_y_display = widgets.Label(
        value=f"Posición Y actual: {y_slider.value if 'y_slider' in globals() else Config.DEFAULT_OFFSET_Y}"
    )

    # Checkbox para log detallado
    verbose_check = widgets.Checkbox(
        value=True,  # Activado por defecto para ver el progreso
        description='📋 Mostrar log detallado durante procesamiento'
    )

    # Botón de procesamiento principal
    process_button = widgets.Button(
        description='🚀 PROCESAR TODO',
        button_style='success',
        layout=widgets.Layout(width='200px', height='50px'),
        icon='rocket'
    )

    # Área de resultados
    results_output = widgets.Output()

    def on_process_click(b):
        with results_output:
            clear_output(wait=True)

            print("🚀 INICIANDO PROCESAMIENTO COMPLETO")
            print("="*50)

            start_time = time.time()
            current_y = y_slider.value if 'y_slider' in globals() else Config.DEFAULT_OFFSET_Y

            # Configurar verbosidad
            processor.logger.set_verbose(verbose_check.value)

            try:
                # Barra de progreso para procesamiento
                dept_progress = widgets.IntProgress(
                    value=0,
                    min=0,
                    max=100,
                    description='Progreso:',
                    bar_style='success',
                    style={'bar_color': '#28a745'},
                    orientation='horizontal'
                )

                dept_label = widgets.Label(value="Iniciando procesamiento...")
                progress_display = widgets.VBox([dept_progress, dept_label])
                display(progress_display)

                # Callback de progreso
                def update_dept_progress(current, total, message):
                    dept_progress.value = int((current / total) * 100)
                    dept_label.value = f"{message} ({current}/{total})"

                # Ejecutar procesamiento
                success, message = processor.process_all(current_y, update_dept_progress)

                if success:
                    print(f"\n✅ {message}")

                    # Crear ZIP
                    print("\n📦 Creando archivo ZIP organizado...")
                    zip_file = processor.create_zip()

                    # Estadísticas finales
                    end_time = time.time()
                    duration = end_time - start_time
                    final_stats = processor.get_statistics()

                    print(f"\n🎉 ¡PROCESAMIENTO COMPLETADO!")
                    print("="*50)
                    print(f"⏱️ Tiempo total: {duration:.1f} segundos")
                    print(f"📁 Departamentos creados: {len(final_stats['directory_stats'])}")
                    print(f"📄 Archivos PDF generados: {sum(final_stats['directory_stats'].values())}")
                    print(f"📦 Archivo ZIP: {zip_file}")

                    # Mostrar estructura final
                    print(f"\n📂 ESTRUCTURA FINAL CREADA:")
                    for dept, count in final_stats['directory_stats'].items():
                        print(f"   📁 {dept}/ → {count} archivos")

                    # Botón de descarga
                    print(f"\n⬇️ DESCARGA TU ARCHIVO ORGANIZADO:")
                    files.download(zip_file)

                    if verbose_check.value:
                        print(f"\n📋 LOG COMPLETO DEL PROCESAMIENTO:")
                        print("-" * 50)
                        print(processor.logger.get_full_log())

                else:
                    print(f"❌ {message}")
                    print(f"\n📋 Log de errores:")
                    print(processor.logger.get_full_log())

            except Exception as e:
                print(f"💥 ERROR INESPERADO: {e}")
                import traceback
                print(f"\n🔍 Traceback completo:")
                print(traceback.format_exc())

    process_button.on_click(on_process_click)

    # Mostrar interfaz final
    print(f"\n⚙️ CONFIGURACIÓN FINAL:")
    display(final_y_display)
    display(verbose_check)
    print()
    display(process_button)
    display(results_output)

else:
    print("⚠️ Ejecuta primero el paso 3 para analizar el PDF")
    print("\n🔍 Estado actual:")
    if 'processor' not in globals():
        print("❌ Procesador no inicializado")
    elif not hasattr(processor, 'department_data'):
        print("❌ No hay datos de departamentos")
    elif not processor.department_data:
        print("❌ No se encontraron departamentos válidos")
    else:
        print("✅ Estado parece correcto, reintenta ejecutar esta celda")