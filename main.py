#!/usr/bin/env python3
"""
Sistema Profesional de Firmado PDF
Procesamiento automatizado de documentos PDF con firmas digitales
"""

import os
import re
import shutil
import zipfile
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Tuple, Optional
import time
import threading
from concurrent.futures import ThreadPoolExecutor

# Librerías principales
try:
    import fitz  # PyMuPDF
    import pdfplumber
    from PIL import Image
    from tqdm import tqdm
except ImportError as e:
    print(f"❌ Error importing required libraries: {e}")
    print("📦 Please install dependencies:")
    print("   pip install PyMuPDF pdfplumber pillow tqdm")
    print("\n🔧 Installation commands:")
    print("   pip install PyMuPDF")
    print("   pip install pdfplumber")
    print("   pip install pillow")
    print("   pip install tqdm")
    exit(1)

print("✅ Sistema de firmado PDF - Modo Local")
print("📚 Librerías cargadas: PyMuPDF, PDFPlumber, Pillow, tqdm")

class Config:
    """Configuración centralizada del sistema"""
    # Configuración de firma
    TARGET_TEXTS = [
        "FIRMA UNIDAD DE PERSONAL",
        "FIRMA:",
        "FIRMA AUTORIZADA", 
        "VALIDACIÓN",
        "FIRMA DEL EMPLEADO",
        "EMPLEADO",
        "TRABAJADOR"
    ]

    # Configuración de posición
    DEFAULT_OFFSET_X = 0
    DEFAULT_OFFSET_Y = 620
    DEFAULT_WIDTH = 180
    DEFAULT_HEIGHT = 70

    # Configuración de validación MEJORADA
    VALIDATION_KEYWORDS = [
        "LIQUIDACIÓN DE REMUNERACIONES",
        "LIQUIDACION DE REMUNERACIONES", 
        "RUT",
        "HABERES",
        "DESCUENTOS",
        "LÍQUIDO A PAGAR",
        "LIQUIDO A PAGAR",
        "EMPLEADO",
        "TRABAJADOR",
        "PERIODO",
        "FECHA",
        "DEPARTAMENTO",
        "UNIDAD",
        "CARGO",
        "SUELDO",
        "REMUNERACIÓN",
        "REMUNERACION"
    ]

    # Patrones de limpieza MEJORADOS
    CLEANUP_PATTERNS = [
        r'\s*TRAMO.*',
        r'\s*%.*',
        r'\s*-PERS.*',
        r'\s*\(.*?\)',
        r'\s*\[.*?\]',
        r'\s*\d+\s*$',
        r'^\s*\d+[\.\-]\d+[\.\-]\d+.*',
        r'.*CODIGO.*',
        r'.*COD\s+\d+.*',
        r'.*RBD.*'
    ]

class Logger:
    """Sistema de logging mejorado con niveles"""
    
    def __init__(self, verbose: bool = True):
        self.verbose = verbose
        self.start_time = time.time()
        
    def set_verbose(self, verbose: bool):
        self.verbose = verbose
        
    def info(self, message: str, show_always: bool = False):
        if self.verbose or show_always:
            print(f"ℹ️ {message}")
            
    def success(self, message: str):
        print(f"✅ {message}")
        
    def warning(self, message: str):
        print(f"⚠️ {message}")
        
    def error(self, message: str):
        print(f"❌ {message}")
        
    def debug(self, message: str):
        if self.verbose:
            elapsed = time.time() - self.start_time
            print(f"🔍 [{elapsed:.1f}s] {message}")

class TextProcessor:
    """Procesador de texto con métodos estáticos mejorados"""
    
    @staticmethod
    def clean_department_name(dept_name: str) -> str:
        """Limpia y normaliza nombres de departamento"""
        if not dept_name:
            return "SIN_DEPARTAMENTO"
            
        # Normalizar texto
        cleaned = dept_name.strip().upper()
        
        # Aplicar patrones de limpieza
        for pattern in Config.CLEANUP_PATTERNS:
            cleaned = re.sub(pattern, '', cleaned, flags=re.IGNORECASE)
        
        # Limpiezas específicas
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        cleaned = re.sub(r'^[^\w]+|[^\w]+$', '', cleaned)
        
        # Mapeos específicos para Galvarino
        dept_mappings = {
            'EDUCACION': 'DEPARTAMENTO_EDUCACION',
            'EDUCACIÓN': 'DEPARTAMENTO_EDUCACION',
            'SALUD': 'DEPARTAMENTO_SALUD',
            'OBRAS': 'DEPARTAMENTO_OBRAS',
            'ADMINISTRACION': 'ADMINISTRACION_MUNICIPAL',
            'ADMINISTRACIÓN': 'ADMINISTRACION_MUNICIPAL',
            'FINANZAS': 'DEPARTAMENTO_FINANZAS',
            'SECRETARIA': 'SECRETARIA_MUNICIPAL',
            'SECRETARÍA': 'SECRETARIA_MUNICIPAL'
        }
        
        for key, value in dept_mappings.items():
            if key in cleaned:
                return value
                
        return cleaned if cleaned else "SIN_DEPARTAMENTO"
    
    @staticmethod
    def clean_employee_name(name: str) -> str:
        """Limpia y normaliza nombres de empleados"""
        if not name:
            return "SIN_NOMBRE"
            
        # Normalizar
        cleaned = name.strip()
        
        # Remover patrones comunes
        patterns_to_remove = [
            r'TRAMO\s+[A-Z0-9]+',
            r'%\s*\d+',
            r'PERS\s+\d+',
            r'^\d+[\.\-]\d+[\.\-]\d+',
            r'RUT\s*:?\s*\d+',
            r'CODIGO\s*:?\s*\d+',
            r'COD\s+\d+'
        ]
        
        for pattern in patterns_to_remove:
            cleaned = re.sub(pattern, '', cleaned, flags=re.IGNORECASE)
        
        # Limpiar espacios y caracteres especiales
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        cleaned = re.sub(r'^[^\w]+|[^\w]+$', '', cleaned)
        
        return cleaned if cleaned else "SIN_NOMBRE"
    
    @staticmethod
    def extract_page_info(page_text: str) -> Dict[str, any]:
        """Extrae información de empleado y departamento de una página"""
        lines = [line.strip() for line in page_text.split('\n') if line.strip()]
        normalized_text = ' '.join(lines).upper()
        
        # Patrones para nombres de empleados MEJORADOS
        name_patterns = [
            r'(?:EMPLEADO|TRABAJADOR|NOMBRE)\s*:?\s*([A-ZÁÉÍÓÚÑÜ][A-ZÁÉÍÓÚÑÜ\s]{2,50})',
            r'^([A-ZÁÉÍÓÚÑÜ][A-ZÁÉÍÓÚÑÜ\s]{5,40})\s+(?:RUT|CARGO|DEPARTAMENTO)',
            r'([A-ZÁÉÍÓÚÑÜ][A-ZÁÉÍÓÚÑÜ\s]{5,40})\s+\d{1,2}[\.\-]\d{3}[\.\-]\d{3}',
            r'(?:^|\n)([A-ZÁÉÍÓÚÑÜ][A-ZÁÉÍÓÚÑÜ\s]{5,40})(?:\s+(?:TRAMO|%|\d+))',
        ]
        
        # Patrones para departamentos MEJORADOS
        dept_patterns = [
            r'(?:DEPARTAMENTO|DEPTO|DEPT|UNIDAD|AREA|ÁREA)\s*:?\s*([A-ZÁÉÍÓÚÑÜ][A-ZÁÉÍÓÚÑÜ\s]{3,80})',
            r'(?:^|\n)([A-ZÁÉÍÓÚÑÜ][A-ZÁÉÍÓÚÑÜ\s]{10,80})\s*(?:DEPARTAMENTO|DEPTO)',
            r'DEPARTAMENTO\s+DE\s+([A-ZÁÉÍÓÚÑÜ][A-ZÁÉÍÓÚÑÜ\s]{3,60})',
            r'([A-ZÁÉÍÓÚÑÜ][A-ZÁÉÍÓÚÑÜ\s]{8,60})\s+(?:MUNICIPAL|MUNICIPALIDAD)',
        ]

        # Buscar nombre con validación MEJORADA
        employee_name = "SIN_NOMBRE"
        raw_name_found = ""

        for i, pattern in enumerate(name_patterns):
            matches = re.finditer(pattern, normalized_text, re.IGNORECASE | re.MULTILINE)
            for match in matches:
                raw_name = match.group(1).strip()
                
                # Filtros de validación MEJORADOS
                if (len(raw_name) >= 3 and len(raw_name) <= 60 and
                    not re.search(r'\d{2}[/\-\.]\d{2}[/\-\.]\d{4}', raw_name) and  # fechas
                    not re.search(r'^\d+[\.\-]\d+[\.\-]\d+', raw_name) and  # RUT al inicio
                    not re.search(r'(?:ingreso|fecha|codigo|cod|rbd|tramo)', raw_name, re.IGNORECASE) and
                    len(re.findall(r'[A-ZÁÉÍÓÚÑÜ]', raw_name)) >= 3):  # Mínimo 3 letras
                    
                    raw_name_found = raw_name
                    employee_name = TextProcessor.clean_employee_name(raw_name)
                    break
            
            if employee_name != "SIN_NOMBRE":
                break

        # Buscar departamento con validación MEJORADA
        department = "SIN_DEPARTAMENTO"
        raw_dept_found = ""

        # NUEVA: Buscar en las primeras líneas del texto
        first_lines = '\n'.join(lines)
        
        for i, pattern in enumerate(dept_patterns):
            # Buscar en texto completo
            matches = re.finditer(pattern, normalized_text, re.IGNORECASE | re.MULTILINE)
            for match in matches:
                raw_dept = match.group(1).strip()
                
                # Filtros de validación MEJORADOS pero MÁS PERMISIVOS
                if (len(raw_dept) >= 3 and len(raw_dept) <= 120 and
                    not re.search(r'\d{2}[/\-\.]\d{2}[/\-\.]\d{4}', raw_dept) and  # fechas
                    not re.search(r'^\d+[\.\-]\d+[\.\-]\d+', raw_dept) and  # RUT al inicio
                    not re.search(r'(?:ingreso|fecha|codigo|cod|rbd)', raw_dept, re.IGNORECASE) and
                    len(re.findall(r'[A-ZÁÉÍÓÚÑÜ]', raw_dept)) >= 3):  # Mínimo 3 letras

                    raw_dept_found = raw_dept
                    department = TextProcessor.clean_department_name(raw_dept)
                    break
            
            if department != "SIN_DEPARTAMENTO":
                break

        # SI NO SE ENCUENTRA DEPARTAMENTO, usar uno genérico basado en contenido
        if department == "SIN_DEPARTAMENTO":
            # Buscar palabras clave que indiquen el tipo de institución
            if any(word in normalized_text.upper() for word in ['EDUCACION', 'EDUCACIÓN', 'ESCUELA', 'LICEO', 'COLEGIO']):
                department = "DEPARTAMENTO_EDUCACION"
                raw_dept_found = "EDUCACION (detectado por contenido)"
            elif any(word in normalized_text.upper() for word in ['MUNICIPAL', 'MUNICIPALIDAD']):
                department = "ADMINISTRACION_MUNICIPAL"
                raw_dept_found = "MUNICIPAL (detectado por contenido)"
            else:
                department = "DEPARTAMENTO_GENERAL"
                raw_dept_found = "GENERAL (por defecto)"

        # Validación final del contenido
        keyword_count = sum(1 for keyword in Config.VALIDATION_KEYWORDS 
                          if keyword.upper() in normalized_text.upper())
        
        additional_count = len(re.findall(r'\$\s*[\d,]+', normalized_text))
        
        is_valid = (keyword_count >= 1 or additional_count >= 1 or 
                   (employee_name != "SIN_NOMBRE" and len(raw_name_found) > 5))

        return {
            "employee_name": employee_name,
            "department": department,
            "is_valid": is_valid,
            "raw_name": raw_name_found,
            "raw_dept": raw_dept_found,
            "total_score": keyword_count + additional_count,
            "debug_lines": lines[:5]  # Para debugging
        }

class DirectoryManager:
    """Gestor de directorios y organización de archivos"""

    def __init__(self, base_output_dir: str = "liquidaciones_firmadas"):
        self.base_output_dir = Path(base_output_dir)
        self.department_structure = {}
        self.created_dirs = set()

    def setup_base_directory(self):
        """Configura el directorio base"""
        if self.base_output_dir.exists():
            shutil.rmtree(self.base_output_dir)
        self.base_output_dir.mkdir(exist_ok=True)

    def create_department_directory(self, department_name: str) -> Path:
        """Crea directorio para un departamento específico"""
        safe_dept_name = self._sanitize_directory_name(department_name)
        dept_dir = self.base_output_dir / safe_dept_name

        if dept_dir not in self.created_dirs:
            dept_dir.mkdir(exist_ok=True)
            self.created_dirs.add(dept_dir)
            self.department_structure[department_name] = {
                'path': dept_dir,
                'safe_name': safe_dept_name,
                'employee_count': 0
            }

        return dept_dir

    def _sanitize_directory_name(self, name: str) -> str:
        """Sanitiza nombres para uso como directorios"""
        # Remover caracteres problemáticos
        safe_name = re.sub(r'[<>:"/\\|?*]', '_', name)
        safe_name = re.sub(r'\s+', '_', safe_name)
        safe_name = safe_name.strip('._')

        # Limitar longitud
        if len(safe_name) > 50:
            safe_name = safe_name[:50]

        return safe_name if safe_name else "DEPARTAMENTO_DESCONOCIDO"

    def generate_employee_filename(self, employee_name: str, page_num: int = None) -> str:
        """Genera nombre de archivo único para empleado"""
        safe_name = self._sanitize_directory_name(employee_name)

        if page_num is not None:
            return f"{safe_name}_pag_{page_num:03d}.pdf"
        else:
            return f"{safe_name}.pdf"

    def get_statistics(self) -> Dict:
        """Retorna estadísticas de la estructura creada"""
        return {
            'total_departments': len(self.department_structure),
            'total_directories': len(self.created_dirs),
            'departments': {name: info['employee_count']
                          for name, info in self.department_structure.items()}
        }

class SignaturePlacer:
    """Colocador de firmas con detección inteligente de posición"""

    def __init__(self, signature_path: str, logger: Logger):
        self.signature_path = signature_path
        self.logger = logger
        self.signature_image = None
        self._load_signature()

    def _load_signature(self):
        """Carga la imagen de firma"""
        try:
            self.signature_image = Image.open(self.signature_path)
            if self.signature_image.mode != 'RGBA':
                self.signature_image = self.signature_image.convert('RGBA')
            self.logger.success(f"Firma cargada: {self.signature_image.size}")
        except Exception as e:
            self.logger.error(f"Error cargando firma: {e}")
            raise

    def find_position(self, page: fitz.Page, offset_y: float = None) -> Tuple[float, float, bool]:
        """Encuentra la mejor posición para colocar la firma"""
        page_text = page.get_text().upper()
        page_rect = page.rect

        # Buscar textos objetivo
        for target_text in Config.TARGET_TEXTS:
            text_instances = page.search_for(target_text)
            if text_instances:
                # Usar la primera instancia encontrada
                rect = text_instances[0]
                x = rect.x0 + Config.DEFAULT_OFFSET_X
                y = rect.y1 + (offset_y if offset_y is not None else Config.DEFAULT_OFFSET_Y)

                # Asegurar que esté dentro de los límites de la página
                x = max(0, min(x, page_rect.width - Config.DEFAULT_WIDTH))
                y = max(0, min(y, page_rect.height - Config.DEFAULT_HEIGHT))

                self.logger.debug(f"Posición encontrada para '{target_text}': ({x:.1f}, {y:.1f})")
                return x, y, False

        # Posición de fallback (esquina inferior derecha)
        fallback_x = page_rect.width - Config.DEFAULT_WIDTH - 20
        fallback_y = page_rect.height - Config.DEFAULT_HEIGHT - 20

        self.logger.warning("Usando posición de fallback")
        return fallback_x, fallback_y, True

    def apply_signature(self, page: fitz.Page, x: float, y: float) -> fitz.Rect:
        """Aplica la firma en la posición especificada"""
        try:
            # Crear rectángulo para la firma
            signature_rect = fitz.Rect(x, y, x + Config.DEFAULT_WIDTH, y + Config.DEFAULT_HEIGHT)

            # Insertar imagen
            page.insert_image(signature_rect, filename=self.signature_path)

            self.logger.debug(f"Firma aplicada en: {signature_rect}")
            return signature_rect

        except Exception as e:
            self.logger.error(f"Error aplicando firma: {e}")
            raise

class PDFProcessor:
    """Procesador principal de documentos PDF"""

    def __init__(self, pdf_path: str, signature_path: str, verbose: bool = True):
        self.pdf_path = pdf_path
        self.signature_path = signature_path
        self.logger = Logger(verbose)

        # Componentes
        self.directory_manager = DirectoryManager()
        self.signer = SignaturePlacer(signature_path, self.logger)

        # Datos procesados
        self.pages_data = []
        self.department_data = defaultdict(list)
        self.statistics = {}

    def analyze_pdf(self, progress_callback=None) -> Tuple[bool, str]:
        """Analiza el PDF y extrae información de cada página"""
        try:
            self.logger.info("Iniciando análisis del PDF...")

            with pdfplumber.open(self.pdf_path) as pdf:
                total_pages = len(pdf.pages)
                self.logger.info(f"Total de páginas: {total_pages}")

                valid_pages = 0

                for page_num, page in enumerate(pdf.pages, 1):
                    if progress_callback:
                        progress_callback(page_num, total_pages)

                    # Extraer texto de la página
                    page_text = page.extract_text() or ""

                    # Procesar información
                    page_info = TextProcessor.extract_page_info(page_text)
                    page_info["page_num"] = page_num

                    # Agregar a datos
                    self.pages_data.append(page_info)

                    if page_info["is_valid"]:
                        valid_pages += 1
                        dept = page_info["department"]
                        self.department_data[dept].append(page_info)

                # Generar estadísticas
                self.statistics = {
                    'total_pages': total_pages,
                    'valid_pages': valid_pages,
                    'invalid_pages': total_pages - valid_pages,
                    'departments_found': len(self.department_data),
                    'department_breakdown': {dept: len(pages)
                                           for dept, pages in self.department_data.items()}
                }

                self.logger.success(f"Análisis completado: {valid_pages}/{total_pages} páginas válidas")
                return True, f"Análisis exitoso: {valid_pages} páginas válidas en {len(self.department_data)} departamentos"

        except Exception as e:
            error_msg = f"Error en análisis: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg

    def generate_preview(self, offset_y: float = None) -> Tuple[bool, str, bytes]:
        """Genera preview de la primera página con firma"""
        try:
            if not self.pages_data:
                return False, "No hay datos analizados", b""

            self.logger.info("Generando preview...")

            with fitz.open(self.pdf_path) as pdf:
                first_page_num = self.pages_data[0]["page_num"] - 1

                # Crear documento temporal para preview
                preview_doc = fitz.open()
                preview_page = preview_doc.new_page(width=pdf[first_page_num].rect.width,
                                                  height=pdf[first_page_num].rect.height)

                # Copiar contenido de la página original
                preview_page.show_pdf_page(preview_page.rect, pdf, first_page_num)

                # Aplicar firma
                x, y, is_fallback = self.signer.find_position(preview_page, offset_y)
                self.signer.apply_signature(preview_page, x, y)

                # Convertir a imagen con mejor calidad
                mat = fitz.Matrix(2.0, 2.0)  # Escala 2x para mejor calidad
                pix = preview_page.get_pixmap(matrix=mat)
                img_data = pix.tobytes("png")

                preview_doc.close()

                status = "Preview generado (posición de fallback)" if is_fallback else "Preview generado"
                self.logger.success(status)
                return True, status, img_data

        except Exception as e:
            error_msg = f"Error generando preview: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, b""

    def process_all(self, offset_y: float = None, progress_callback=None) -> Tuple[bool, str]:
        """Procesa todas las páginas y genera archivos individuales"""
        try:
            if not self.department_data:
                return False, "No hay datos de departamentos para procesar"

            self.logger.info("Iniciando procesamiento completo...")

            # Configurar directorio base
            self.directory_manager.setup_base_directory()

            total_processed = 0

            with fitz.open(self.pdf_path) as original_pdf:

                for dept_name, pages_info in self.department_data.items():
                    self.logger.info(f"Procesando departamento: {dept_name} ({len(pages_info)} páginas)")

                    # Crear directorio del departamento
                    dept_dir = self.directory_manager.create_department_directory(dept_name)
                    self.logger.info(f"📂 Directorio creado: {dept_dir}")

                    # Procesar cada empleado individualmente
                    for emp_idx, page_info in enumerate(pages_info):
                        try:
                            page_num = page_info["page_num"]
                            employee_name = page_info["employee_name"]

                            if progress_callback:
                                progress_callback(total_processed + emp_idx + 1,
                                                sum(len(pages) for pages in self.department_data.values()))

                            # Crear documento individual
                            individual_doc = fitz.open()
                            original_page = original_pdf[page_num - 1]

                            new_page = individual_doc.new_page(width=original_page.rect.width,
                                                             height=original_page.rect.height)
                            new_page.show_pdf_page(new_page.rect, original_pdf, original_page.number)

                            # Aplicar firma
                            x, y, is_fallback = self.signer.find_position(new_page, offset_y)
                            signature_rect = self.signer.apply_signature(new_page, x, y)

                            # GENERAR NOMBRE DE ARCHIVO ÚNICO
                            filename = self.directory_manager.generate_employee_filename(employee_name, page_num)
                            output_path = dept_dir / filename

                            # Guardar documento individual
                            individual_doc.save(str(output_path))
                            individual_doc.close()

                            self.logger.debug(f"✅ Guardado: {output_path}")

                        except Exception as e:
                            self.logger.error(f"Error procesando página {page_num}: {e}")
                            continue

                    # Actualizar contador de empleados en departamento
                    if dept_name in self.directory_manager.department_structure:
                        self.directory_manager.department_structure[dept_name]['employee_count'] = len(pages_info)

                    total_processed += len(pages_info)

            self.logger.success(f"Procesamiento completado: {total_processed} archivos generados")
            return True, f"Procesamiento exitoso: {total_processed} archivos en {len(self.department_data)} departamentos"

        except Exception as e:
            error_msg = f"Error en procesamiento: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg

    def create_zip(self) -> str:
        """Crea archivo ZIP con todos los documentos organizados"""
        zip_path = "liquidaciones_firmadas_organizadas.zip"

        if Path(zip_path).exists():
            Path(zip_path).unlink()

        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED, compresslevel=6) as zf:

            base_dir = self.directory_manager.base_output_dir

            for file_path in base_dir.rglob("*.pdf"):
                # Mantener estructura de directorios en el ZIP
                arc_path = file_path.relative_to(base_dir)
                zf.write(file_path, arc_path)
                self.logger.debug(f"Añadido al ZIP: {arc_path}")

        file_size = Path(zip_path).stat().st_size / (1024*1024)  # MB
        self.logger.success(f"ZIP creado: {zip_path} ({file_size:.1f} MB)")
        return zip_path

    def get_detailed_statistics(self) -> Dict:
        """Retorna estadísticas detalladas del procesamiento"""
        stats = self.statistics.copy()
        stats.update(self.directory_manager.get_statistics())
        return stats

class FileManager:
    """Gestor de archivos para entorno local"""

    @staticmethod
    def setup_environment():
        """Limpia el entorno de trabajo"""
        # Limpiar archivos temporales
        temp_files = ["*.zip", "liquidaciones_firmadas"]
        for pattern in temp_files:
            for file_path in Path(".").glob(pattern):
                if file_path.is_file():
                    file_path.unlink()
                elif file_path.is_dir():
                    shutil.rmtree(file_path)

        print("🧹 Entorno limpiado")

    @staticmethod
    def validate_pdf(pdf_path: str) -> Tuple[Optional[str], str]:
        """Valida y carga archivo PDF desde ruta local"""
        if not Path(pdf_path).exists():
            return None, f"❌ Archivo no encontrado: {pdf_path}"

        if not pdf_path.lower().endswith('.pdf'):
            return None, "❌ El archivo debe ser un PDF"

        try:
            with pdfplumber.open(pdf_path) as pdf:
                page_count = len(pdf.pages)

            print(f"✅ PDF válido: '{pdf_path}' ({page_count} páginas)")
            return pdf_path, f"✅ {page_count} páginas encontradas"

        except Exception as e:
            return None, f"❌ Error al abrir PDF: {e}"

    @staticmethod
    def validate_signature(signature_path: str) -> Tuple[Optional[str], str]:
        """Valida imagen de firma desde ruta local"""
        if not Path(signature_path).exists():
            return None, f"❌ Archivo no encontrado: {signature_path}"

        if not signature_path.lower().endswith(('.png', '.jpg', '.jpeg')):
            return None, "❌ La firma debe ser PNG, JPG o JPEG"

        try:
            with Image.open(signature_path) as img:
                # Convertir a RGBA si no lo es (para compatibilidad)
                if img.mode != 'RGBA':
                    print(f"⚠️ Convirtiendo imagen a RGBA para mejor compatibilidad")
                    img = img.convert('RGBA')
                    # Guardar versión convertida
                    new_path = signature_path.rsplit('.', 1)[0] + '_rgba.png'
                    img.save(new_path)
                    signature_path = new_path
                    print(f"💾 Imagen convertida guardada como: {new_path}")

                width, height = img.size

            print(f"✅ Firma válida: '{signature_path}' ({width}x{height}px)")
            return signature_path, f"✅ Firma lista ({width}x{height}px)"

        except Exception as e:
            return None, f"❌ Error validando firma: {e}"

def main():
    """Función principal para uso en línea de comandos"""
    import argparse

    parser = argparse.ArgumentParser(
        description='🔏 Sistema de Firmado PDF - Procesamiento Local',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Ejemplos de uso:
  python main.py documento.pdf firma.png
  python main.py liquidaciones.pdf mi_firma.png --offset-y 50 --verbose
  python main.py /ruta/completa/doc.pdf /ruta/firma.png --clean
        """
    )

    parser.add_argument('pdf_path', help='Ruta al archivo PDF con liquidaciones')
    parser.add_argument('signature_path', help='Ruta a la imagen de firma (PNG/JPG)')
    parser.add_argument('--offset-y', type=float, default=None,
                       help='Ajuste vertical para posición de firma (pixeles)')
    parser.add_argument('--verbose', action='store_true',
                       help='Mostrar información detallada del proceso')
    parser.add_argument('--clean', action='store_true',
                       help='Limpiar archivos temporales antes de procesar')

    args = parser.parse_args()

    print("🔏 Sistema de Firmado PDF - Iniciando...")
    print("=" * 50)

    # Limpiar entorno si se solicita
    if args.clean:
        FileManager.setup_environment()

    # Validar archivos
    print("📋 Validando archivos...")

    pdf_path, pdf_status = FileManager.validate_pdf(args.pdf_path)
    if not pdf_path:
        print(pdf_status)
        return 1

    signature_path, sig_status = FileManager.validate_signature(args.signature_path)
    if not signature_path:
        print(sig_status)
        return 1

    print("\n🔄 Iniciando procesamiento...")

    # Crear procesador
    processor = PDFProcessor(pdf_path, signature_path, args.verbose)

    # Analizar PDF
    print("📖 Analizando documento PDF...")
    success, message = processor.analyze_pdf()
    if not success:
        print(f"❌ {message}")
        return 1
    print(f"✅ {message}")

    # Mostrar preview de estadísticas
    stats = processor.get_detailed_statistics()
    print(f"\n📊 Resumen del análisis:")
    print(f"   📄 Total páginas: {stats['total_pages']}")
    print(f"   ✅ Páginas válidas: {stats['valid_pages']}")
    print(f"   🏢 Departamentos encontrados: {stats['departments_found']}")

    if stats['departments_found'] > 0:
        print("   📂 Distribución por departamento:")
        for dept, count in stats['department_breakdown'].items():
            print(f"      • {dept}: {count} empleados")

    # Procesar todas las páginas
    print(f"\n🚀 Procesando {stats['valid_pages']} páginas...")
    success, message = processor.process_all(args.offset_y)
    if not success:
        print(f"❌ {message}")
        return 1
    print(f"✅ {message}")

    # Crear archivo ZIP
    print("\n📦 Creando archivo comprimido...")
    zip_path = processor.create_zip()

    # Resumen final
    print("\n" + "=" * 50)
    print("🎉 ¡PROCESO COMPLETADO EXITOSAMENTE!")
    print("=" * 50)
    print(f"📁 Archivo ZIP generado: {zip_path}")
    print(f"📊 Estadísticas finales:")
    print(f"   📄 Páginas procesadas: {stats['valid_pages']}")
    print(f"   🏢 Departamentos: {stats['departments_found']}")
    print(f"   📁 Archivos individuales creados: {stats['valid_pages']}")

    return 0

if __name__ == "__main__":
    exit(main())
