#!/usr/bin/env python3
"""
🏗️ Constructor de Aplicación - Sistema de Firmado PDF
Genera ejecutables para Mac (.app) y Windows (.exe)
"""

import subprocess
import sys
import platform
import os
import shutil
from pathlib import Path

def print_header():
    """Mostrar header del constructor"""
    print("=" * 60)
    print("🏗️ CONSTRUCTOR DE APLICACIÓN - SISTEMA DE FIRMADO PDF")
    print("=" * 60)
    print(f"Sistema operativo: {platform.system()} {platform.release()}")
    print(f"Python: {sys.version}")
    print("=" * 60)

def check_pyinstaller():
    """Verificar e instalar PyInstaller si es necesario"""
    print("🔧 Verificando PyInstaller...")
    
    try:
        import PyInstaller
        print("✅ PyInstaller disponible")
        return True
    except ImportError:
        print("📦 Instalando PyInstaller...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], 
                         check=True, capture_output=True)
            print("✅ PyInstaller instalado")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Error instalando PyInstaller: {e}")
            return False

def create_spec_file():
    """Crear archivo .spec personalizado para PyInstaller"""
    print("📝 Creando archivo de configuración...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['app_gui.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'PIL._tkinter_finder',
        'tkinter',
        'tkinter.ttk',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'tkinter.scrolledtext',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'numpy',
        'scipy',
        'pandas',
        'jupyter',
        'IPython',
        'google.colab'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='Sistema_Firmado_PDF',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if platform.system() == 'Windows' else 'icon.icns',
)

# Para macOS, crear bundle .app
if platform.system() == 'Darwin':
    app = BUNDLE(
        exe,
        name='Sistema Firmado PDF.app',
        icon='icon.icns',
        bundle_identifier='com.sistemafirmado.pdf',
        info_plist={
            'CFBundleName': 'Sistema Firmado PDF',
            'CFBundleDisplayName': 'Sistema de Firmado PDF',
            'CFBundleVersion': '1.0.0',
            'CFBundleShortVersionString': '1.0.0',
            'NSHighResolutionCapable': True,
            'LSMinimumSystemVersion': '10.13.0',
        },
    )
'''
    
    with open('app.spec', 'w') as f:
        f.write(spec_content)
    
    print("✅ Archivo de configuración creado: app.spec")

def create_icon():
    """Crear icono para la aplicación"""
    print("🎨 Creando icono de aplicación...")
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # Crear icono simple
        size = 512
        img = Image.new('RGBA', (size, size), (255, 255, 255, 0))
        draw = ImageDraw.Draw(img)
        
        # Fondo circular azul
        margin = 50
        draw.ellipse([margin, margin, size-margin, size-margin], 
                    fill=(52, 152, 219, 255))
        
        # Texto del icono
        try:
            font_size = size // 6
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            font = ImageFont.load_default()
        
        text = "PDF"
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        x = (size - text_width) // 2
        y = (size - text_height) // 2 - 20
        
        draw.text((x, y), text, fill=(255, 255, 255, 255), font=font)
        
        # Símbolo de firma
        pen_y = y + text_height + 20
        draw.line([x, pen_y, x + text_width, pen_y], fill=(255, 255, 255, 255), width=8)
        
        # Guardar iconos
        if platform.system() == "Windows":
            # ICO para Windows
            img.save('icon.ico', format='ICO', sizes=[(256, 256), (128, 128), (64, 64), (32, 32), (16, 16)])
            print("✅ Icono Windows creado: icon.ico")
        
        elif platform.system() == "Darwin":
            # ICNS para macOS
            img.save('icon.png')
            # Convertir a ICNS (requiere herramientas de macOS)
            try:
                subprocess.run(['sips', '-s', 'format', 'icns', 'icon.png', '--out', 'icon.icns'], 
                             check=True, capture_output=True)
                os.remove('icon.png')
                print("✅ Icono macOS creado: icon.icns")
            except:
                print("⚠️ No se pudo crear ICNS, usando PNG")
                os.rename('icon.png', 'icon.icns')
        
        return True
        
    except Exception as e:
        print(f"⚠️ Error creando icono: {e}")
        return False

def build_executable():
    """Construir el ejecutable"""
    print("🔨 Construyendo aplicación...")
    
    # Limpiar builds anteriores
    for folder in ['build', 'dist']:
        if Path(folder).exists():
            shutil.rmtree(folder)
            print(f"🧹 Limpiado: {folder}/")
    
    try:
        # Ejecutar PyInstaller
        cmd = [sys.executable, "-m", "PyInstaller", "app.spec", "--clean"]
        
        print("⚙️ Ejecutando PyInstaller...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Aplicación construida exitosamente")
            return True
        else:
            print("❌ Error en construcción:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Error ejecutando PyInstaller: {e}")
        return False

def package_application():
    """Empaquetar la aplicación final"""
    print("📦 Empaquetando aplicación...")
    
    dist_path = Path("dist")
    if not dist_path.exists():
        print("❌ No se encontró carpeta dist/")
        return False
    
    system = platform.system()
    
    if system == "Windows":
        # Para Windows, crear ZIP
        exe_path = dist_path / "Sistema_Firmado_PDF.exe"
        if exe_path.exists():
            package_name = f"Sistema_Firmado_PDF_Windows_{platform.machine()}.zip"
            
            import zipfile
            with zipfile.ZipFile(package_name, 'w', zipfile.ZIP_DEFLATED) as zf:
                zf.write(exe_path, exe_path.name)
                
                # Agregar README
                readme_content = """
Sistema de Firmado PDF - Windows

INSTRUCCIONES:
1. Extraer todos los archivos
2. Ejecutar Sistema_Firmado_PDF.exe
3. Seleccionar PDF y firma
4. Procesar documentos

REQUISITOS:
- Windows 10 o superior
- Archivos PDF con texto extraíble
- Imagen de firma (PNG recomendado)

SOPORTE:
- Verificar que el antivirus no bloquee el ejecutable
- Ejecutar como administrador si es necesario
"""
                zf.writestr("README.txt", readme_content)
            
            print(f"✅ Paquete Windows creado: {package_name}")
            return True
    
    elif system == "Darwin":
        # Para macOS, crear DMG
        app_path = dist_path / "Sistema Firmado PDF.app"
        if app_path.exists():
            package_name = f"Sistema_Firmado_PDF_macOS_{platform.machine()}.dmg"
            
            try:
                # Crear DMG
                subprocess.run([
                    'hdiutil', 'create', '-volname', 'Sistema Firmado PDF',
                    '-srcfolder', str(app_path), '-ov', '-format', 'UDZO',
                    package_name
                ], check=True, capture_output=True)
                
                print(f"✅ Paquete macOS creado: {package_name}")
                return True
            except:
                # Fallback: crear ZIP
                package_name = f"Sistema_Firmado_PDF_macOS_{platform.machine()}.zip"
                shutil.make_archive(package_name[:-4], 'zip', dist_path, app_path.name)
                print(f"✅ Paquete macOS creado: {package_name}")
                return True
    
    print("⚠️ No se pudo crear paquete para este sistema")
    return False

def cleanup():
    """Limpiar archivos temporales"""
    print("🧹 Limpiando archivos temporales...")
    
    temp_files = ['app.spec', 'icon.ico', 'icon.icns', 'icon.png']
    temp_dirs = ['build', '__pycache__']
    
    for file in temp_files:
        if Path(file).exists():
            os.remove(file)
    
    for dir in temp_dirs:
        if Path(dir).exists():
            shutil.rmtree(dir)
    
    print("✅ Limpieza completada")

def main():
    """Función principal del constructor"""
    print_header()
    
    # Verificar que los archivos necesarios existen
    required_files = ['app_gui.py', 'main.py']
    for file in required_files:
        if not Path(file).exists():
            print(f"❌ Archivo requerido no encontrado: {file}")
            return 1
    
    # Verificar PyInstaller
    if not check_pyinstaller():
        return 1
    
    # Crear archivos necesarios
    create_spec_file()
    create_icon()
    
    # Construir aplicación
    if not build_executable():
        return 1
    
    # Empaquetar
    if not package_application():
        print("⚠️ Aplicación construida pero no empaquetada")
    
    # Limpiar
    cleanup()
    
    print("\n" + "=" * 60)
    print("🎉 ¡CONSTRUCCIÓN COMPLETADA!")
    print("=" * 60)
    print("\n📁 Archivos generados en la carpeta dist/")
    print("📦 Paquete de distribución creado")
    print("\n💡 La aplicación está lista para distribuir")
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        input("\nPresiona Enter para continuar...")
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⚠️ Construcción cancelada por el usuario")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error inesperado: {e}")
        input("\nPresiona Enter para salir...")
        sys.exit(1)
