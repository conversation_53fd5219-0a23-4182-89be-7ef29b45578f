'use client'

import { Suspense, useState, useEffect } from 'react'
import { Canvas } from '@react-three/fiber'
import { Environment, OrbitControls, Stats } from '@react-three/drei'
import { EffectComposer, Bloom, ToneMapping } from '@react-three/postprocessing'
import { ToneMappingMode } from 'postprocessing'
import ForestScene from '@/components/ForestScene'
import LoadingScreen from '@/components/LoadingScreen'
import UI from '@/components/UI'
import AudioManager from '@/components/AudioManager'

export default function Home() {
  const [isLoading, setIsLoading] = useState(true)
  const [showStats, setShowStats] = useState(false)

  useEffect(() => {
    // Simular carga inicial
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 2000)

    return () => clearTimeout(timer)
  }, [])

  return (
    <main className="w-full h-screen overflow-hidden">
      {isLoading && <LoadingScreen />}
      
      <Canvas
        camera={{ 
          position: [0, 2, 5], 
          fov: 75,
          near: 0.1,
          far: 1000
        }}
        shadows
        gl={{ 
          antialias: true,
          alpha: false,
          powerPreference: "high-performance"
        }}
        dpr={[1, 2]}
      >
        <Suspense fallback={null}>
          {/* Iluminación ambiental */}
          <ambientLight intensity={0.3} />
          <directionalLight
            position={[10, 10, 5]}
            intensity={1}
            castShadow
            shadow-mapSize-width={2048}
            shadow-mapSize-height={2048}
            shadow-camera-far={50}
            shadow-camera-left={-10}
            shadow-camera-right={10}
            shadow-camera-top={10}
            shadow-camera-bottom={-10}
          />

          {/* Escena del bosque */}
          <ForestScene />

          {/* Entorno HDR */}
          <Environment preset="forest" background />

          {/* Controles de cámara */}
          <OrbitControls
            enablePan={true}
            enableZoom={true}
            enableRotate={true}
            minDistance={2}
            maxDistance={50}
            maxPolarAngle={Math.PI / 2}
            target={[0, 0, 0]}
          />

          {/* Post-procesamiento */}
          <EffectComposer>
            <Bloom 
              intensity={0.5}
              luminanceThreshold={0.9}
              luminanceSmoothing={0.9}
            />
            <ToneMapping mode={ToneMappingMode.ACES_FILMIC} />
          </EffectComposer>

          {/* Estadísticas de rendimiento */}
          {showStats && <Stats />}
        </Suspense>
      </Canvas>

      {/* Interfaz de usuario */}
      <UI onToggleStats={() => setShowStats(!showStats)} showStats={showStats} />

      {/* Gestor de audio */}
      <AudioManager />
    </main>
  )
}
