import type { Metadata } from 'next'
import './globals.css'

export const metadata: Metadata = {
  title: 'Vale por un Bosque 3D | Explora la Naturaleza Virtual',
  description: 'Una experiencia inmersiva en 3D para explorar un hermoso bosque virtual. Camina entre árboles, escucha los sonidos de la naturaleza y descubre la magia del bosque.',
  keywords: ['bosque 3d', 'naturaleza virtual', 'exploración', 'three.js', 'experiencia inmersiva'],
  authors: [{ name: 'Vale por un Bosque 3D' }],
  creator: 'Vale por un Bosque 3D',
  publisher: 'Vale por un Bosque 3D',
  openGraph: {
    title: 'Vale por un Bosque 3D',
    description: 'Explora un hermoso bosque virtual en 3D',
    url: 'https://vale-por-un-bosque-3d.vercel.app',
    siteName: 'Vale por un Bosque 3D',
    locale: 'es_ES',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Vale por un Bosque 3D',
    description: 'Explora un hermoso bosque virtual en 3D',
  },
  robots: {
    index: true,
    follow: true,
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="es">
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <meta name="theme-color" content="#369936" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
      </head>
      <body className="font-nature antialiased">
        {children}
      </body>
    </html>
  )
}
