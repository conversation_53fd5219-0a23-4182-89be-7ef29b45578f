#!/usr/bin/env python3
"""
🔧 Instalador Automático - Sistema de Firmado PDF
Instala dependencias automáticamente en Mac y Windows
"""

import subprocess
import sys
import platform
import os
from pathlib import Path

def print_header():
    """Mostrar header del instalador"""
    print("=" * 60)
    print("🔧 INSTALADOR AUTOMÁTICO - SISTEMA DE FIRMADO PDF")
    print("=" * 60)
    print(f"Sistema operativo: {platform.system()} {platform.release()}")
    print(f"Python: {sys.version}")
    print(f"Arquitectura: {platform.machine()}")
    print("=" * 60)

def check_python_version():
    """Verificar versión de Python"""
    print("🐍 Verificando versión de Python...")
    
    if sys.version_info < (3, 7):
        print("❌ Error: Se requiere Python 3.7 o superior")
        print(f"   Versión actual: {sys.version}")
        print("   Descarga Python desde: https://www.python.org/downloads/")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro} - OK")
    return True

def check_pip():
    """Verificar que pip está disponible"""
    print("\n📦 Verificando pip...")
    
    try:
        import pip
        print("✅ pip disponible")
        return True
    except ImportError:
        print("❌ pip no encontrado")
        print("   Instala pip desde: https://pip.pypa.io/en/stable/installation/")
        return False

def install_package(package_name, display_name=None):
    """Instalar un paquete específico"""
    if display_name is None:
        display_name = package_name
        
    print(f"\n📥 Instalando {display_name}...")
    
    try:
        # Usar subprocess para instalar
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_name, "--upgrade"
        ], capture_output=True, text=True, check=True)
        
        print(f"✅ {display_name} instalado correctamente")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error instalando {display_name}:")
        print(f"   {e.stderr}")
        return False

def install_dependencies():
    """Instalar todas las dependencias"""
    print("\n🔄 Instalando dependencias del sistema...")
    
    dependencies = [
        ("PyMuPDF", "PyMuPDF (manipulación de PDF)"),
        ("pdfplumber", "pdfplumber (extracción de texto)"),
        ("pillow", "Pillow (procesamiento de imágenes)"),
        ("tqdm", "tqdm (barras de progreso)")
    ]
    
    success_count = 0
    
    for package, description in dependencies:
        if install_package(package, description):
            success_count += 1
        else:
            print(f"⚠️ Falló la instalación de {description}")
    
    print(f"\n📊 Resultado: {success_count}/{len(dependencies)} paquetes instalados")
    return success_count == len(dependencies)

def test_imports():
    """Probar que las importaciones funcionan"""
    print("\n🧪 Probando importaciones...")
    
    test_modules = [
        ("fitz", "PyMuPDF"),
        ("pdfplumber", "pdfplumber"),
        ("PIL", "Pillow"),
        ("tqdm", "tqdm")
    ]
    
    success_count = 0
    
    for module, name in test_modules:
        try:
            __import__(module)
            print(f"✅ {name} - OK")
            success_count += 1
        except ImportError as e:
            print(f"❌ {name} - Error: {e}")
    
    return success_count == len(test_modules)

def create_desktop_shortcut():
    """Crear acceso directo en el escritorio"""
    print("\n🖥️ Creando acceso directo...")
    
    try:
        desktop = Path.home() / "Desktop"
        if not desktop.exists():
            desktop = Path.home() / "Escritorio"  # Español
        
        if not desktop.exists():
            print("⚠️ No se pudo encontrar el escritorio")
            return False
        
        current_dir = Path.cwd()
        app_path = current_dir / "app_gui.py"
        
        if not app_path.exists():
            print("⚠️ No se encontró app_gui.py")
            return False
        
        if platform.system() == "Windows":
            # Crear .bat para Windows
            shortcut_path = desktop / "Sistema de Firmado PDF.bat"
            with open(shortcut_path, 'w', encoding='utf-8') as f:
                f.write(f'@echo off\n')
                f.write(f'cd /d "{current_dir}"\n')
                f.write(f'python app_gui.py\n')
                f.write(f'pause\n')
            
        elif platform.system() == "Darwin":
            # Crear .command para macOS
            shortcut_path = desktop / "Sistema de Firmado PDF.command"
            with open(shortcut_path, 'w') as f:
                f.write(f'#!/bin/bash\n')
                f.write(f'cd "{current_dir}"\n')
                f.write(f'python3 app_gui.py\n')
            
            # Hacer ejecutable
            os.chmod(shortcut_path, 0o755)
        
        else:
            # Linux - crear .desktop
            shortcut_path = desktop / "Sistema de Firmado PDF.desktop"
            with open(shortcut_path, 'w') as f:
                f.write(f'[Desktop Entry]\n')
                f.write(f'Name=Sistema de Firmado PDF\n')
                f.write(f'Comment=Aplicación para firmar PDFs\n')
                f.write(f'Exec=python3 "{app_path}"\n')
                f.write(f'Path={current_dir}\n')
                f.write(f'Terminal=false\n')
                f.write(f'Type=Application\n')
                f.write(f'Categories=Office;\n')
            
            os.chmod(shortcut_path, 0o755)
        
        print(f"✅ Acceso directo creado: {shortcut_path.name}")
        return True
        
    except Exception as e:
        print(f"⚠️ Error creando acceso directo: {e}")
        return False

def show_usage_instructions():
    """Mostrar instrucciones de uso"""
    print("\n" + "=" * 60)
    print("🎉 ¡INSTALACIÓN COMPLETADA!")
    print("=" * 60)
    print("\n📖 CÓMO USAR EL SISTEMA:")
    print("\n1️⃣ Ejecutar la aplicación:")
    print("   • Doble clic en el acceso directo del escritorio")
    print("   • O ejecutar: python app_gui.py")
    print("   • O ejecutar: python3 app_gui.py (en Mac/Linux)")
    
    print("\n2️⃣ Usar la interfaz gráfica:")
    print("   • Seleccionar archivo PDF con liquidaciones")
    print("   • Seleccionar imagen de firma (PNG recomendado)")
    print("   • Ajustar configuración si es necesario")
    print("   • Hacer clic en 'Analizar PDF'")
    print("   • Hacer clic en 'Procesar Todo'")
    
    print("\n3️⃣ Archivos generados:")
    print("   • Carpeta: liquidaciones_firmadas/")
    print("   • ZIP: liquidaciones_firmadas_organizadas.zip")
    
    print("\n💡 CONSEJOS:")
    print("   • Usa imágenes PNG con transparencia para mejores resultados")
    print("   • El PDF debe contener liquidaciones de remuneraciones")
    print("   • Revisa el log para información detallada")
    
    print("\n🆘 SOPORTE:")
    print("   • Si hay problemas, ejecuta: python install.py")
    print("   • Verifica que el PDF tiene texto extraíble")
    print("   • Usa el modo verbose para más información")
    
    print("\n" + "=" * 60)

def check_tkinter():
    """Verificar que tkinter funciona (especial para macOS)"""
    print("\n🖥️ Verificando interfaz gráfica (tkinter)...")

    try:
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()
        root.destroy()
        print("✅ tkinter funciona correctamente")
        return True
    except ImportError as e:
        print(f"❌ Error con tkinter: {e}")

        if platform.system() == "Darwin":  # macOS
            print("\n🍎 Problema común en macOS detectado")
            print("💡 Soluciones disponibles:")
            print("   1. Ejecutar reparación: python3 fix_tkinter.py")
            print("   2. Instalador macOS: python3 install_macos.py")
            print("   3. Python oficial: https://www.python.org/downloads/")

            choice = input("\n¿Intentar reparación automática? (s/n): ")
            if choice.lower().startswith('s'):
                try:
                    import subprocess
                    result = subprocess.run([sys.executable, "fix_tkinter.py"],
                                          capture_output=True, text=True)
                    if result.returncode == 0:
                        # Probar nuevamente
                        try:
                            import tkinter as tk
                            root = tk.Tk()
                            root.withdraw()
                            root.destroy()
                            print("✅ tkinter reparado exitosamente")
                            return True
                        except:
                            print("❌ Reparación falló")
                            return False
                except:
                    print("❌ No se pudo ejecutar reparación automática")

        return False
    except Exception as e:
        print(f"❌ Error inesperado con tkinter: {e}")
        return False

def main():
    """Función principal del instalador"""
    print_header()

    # Verificaciones básicas
    if not check_python_version():
        input("\nPresiona Enter para salir...")
        return 1

    if not check_pip():
        input("\nPresiona Enter para salir...")
        return 1

    # Verificar tkinter (crítico para GUI)
    if not check_tkinter():
        print("\n❌ tkinter no funciona - la aplicación GUI no se puede ejecutar")
        print("\n💡 SOLUCIONES:")

        if platform.system() == "Darwin":
            print("   Para macOS:")
            print("   1. python3 install_macos.py (instalador específico)")
            print("   2. python3 fix_tkinter.py (reparación rápida)")
            print("   3. Instalar Python oficial: https://www.python.org/downloads/")
        else:
            print("   1. Reinstalar Python desde: https://www.python.org/downloads/")
            print("   2. Verificar que tkinter esté incluido en la instalación")

        print("\n⚠️ Sin tkinter, solo funcionará el modo línea de comandos:")
        print("   python main.py archivo.pdf firma.png")

        choice = input("\n¿Continuar sin GUI? (s/n): ")
        if not choice.lower().startswith('s'):
            input("\nPresiona Enter para salir...")
            return 1

    # Instalar dependencias
    if not install_dependencies():
        print("\n❌ No se pudieron instalar todas las dependencias")
        print("💡 Intenta instalar manualmente:")
        print("   pip install PyMuPDF pdfplumber pillow tqdm")
        input("\nPresiona Enter para salir...")
        return 1

    # Probar importaciones
    if not test_imports():
        print("\n❌ Algunas importaciones fallaron")
        print("💡 Intenta reinstalar las dependencias")
        input("\nPresiona Enter para salir...")
        return 1

    # Crear acceso directo
    create_desktop_shortcut()

    # Mostrar instrucciones
    show_usage_instructions()

    input("\nPresiona Enter para continuar...")
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⚠️ Instalación cancelada por el usuario")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error inesperado: {e}")
        input("\nPresiona Enter para salir...")
        sys.exit(1)
