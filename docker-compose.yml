version: '3.8'

services:
  # Base de datos PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: pdf_signature_postgres
    environment:
      POSTGRES_DB: pdf_signatures
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - pdf_signature_network

  # Redis para Celery
  redis:
    image: redis:7-alpine
    container_name: pdf_signature_redis
    ports:
      - "6379:6379"
    networks:
      - pdf_signature_network

  # Backend FastAPI
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: pdf_signature_backend
    environment:
      - DATABASE_URL=********************************************/pdf_signatures
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-secret-key-change-in-production
      - DEBUG=true
    volumes:
      - ./backend:/app
      - pdf_uploads:/app/uploads
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
    networks:
      - pdf_signature_network
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # Celery Worker
  celery_worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: pdf_signature_celery
    environment:
      - DATABASE_URL=********************************************/pdf_signatures
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-secret-key-change-in-production
    volumes:
      - ./backend:/app
      - pdf_uploads:/app/uploads
    depends_on:
      - postgres
      - redis
    networks:
      - pdf_signature_network
    command: celery -A app.workers.celery_app worker --loglevel=info

  # Celery Beat (scheduler)
  celery_beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: pdf_signature_beat
    environment:
      - DATABASE_URL=********************************************/pdf_signatures
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-secret-key-change-in-production
    volumes:
      - ./backend:/app
    depends_on:
      - postgres
      - redis
    networks:
      - pdf_signature_network
    command: celery -A app.workers.celery_app beat --loglevel=info

  # Frontend React
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: pdf_signature_frontend
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
    networks:
      - pdf_signature_network
    command: npm run dev

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: pdf_signature_nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - backend
      - frontend
    networks:
      - pdf_signature_network

volumes:
  postgres_data:
  pdf_uploads:

networks:
  pdf_signature_network:
    driver: bridge
