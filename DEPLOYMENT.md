# 🚀 Guía de Deployment - Vale por un Bosque 3D

## Deployment en Vercel (Recomendado)

### Paso 1: Preparar el Repositorio
```bash
# Inicializar git si no está inicializado
git init

# Agregar todos los archivos
git add .

# Hacer commit inicial
git commit -m "Initial commit: Vale por un Bosque 3D"

# Conectar con GitHub (crear repositorio primero en GitHub)
git remote add origin https://github.com/tu-usuario/vale-por-un-bosque-3d.git
git branch -M main
git push -u origin main
```

### Paso 2: Deployment en Vercel
1. Ve a [vercel.com](https://vercel.com)
2. Haz click en "New Project"
3. Conecta tu cuenta de GitHub
4. Selecciona el repositorio `vale-por-un-bosque-3d`
5. Configura el proyecto:
   - **Framework Preset**: Next.js
   - **Build Command**: `npm run build`
   - **Output Directory**: `.next`
   - **Install Command**: `npm install`

### Paso 3: Variables de Entorno (Opcional)
Si necesitas configuraciones específicas:
```env
NEXT_PUBLIC_SITE_URL=https://tu-dominio.vercel.app
NEXT_PUBLIC_ANALYTICS_ID=tu-analytics-id
```

### Paso 4: Deploy
- Haz click en "Deploy"
- Vercel automáticamente:
  - Instala dependencias
  - Construye el proyecto
  - Despliega la aplicación
- Tu sitio estará disponible en `https://tu-proyecto.vercel.app`

## Deployment en Netlify

### Paso 1: Build Settings
```bash
# Build command
npm run build

# Publish directory
.next
```

### Paso 2: Configuración
Crear `netlify.toml`:
```toml
[build]
  command = "npm run build"
  publish = ".next"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

## Deployment en GitHub Pages

### Configuración para GitHub Pages
Agregar a `next.config.js`:
```javascript
const isProd = process.env.NODE_ENV === 'production'

module.exports = {
  assetPrefix: isProd ? '/vale-por-un-bosque-3d/' : '',
  basePath: isProd ? '/vale-por-un-bosque-3d' : '',
  trailingSlash: true,
  images: {
    unoptimized: true
  }
}
```

### GitHub Actions
Crear `.github/workflows/deploy.yml`:
```yaml
name: Deploy to GitHub Pages

on:
  push:
    branches: [ main ]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18'
        
    - name: Install dependencies
      run: npm install
      
    - name: Build
      run: npm run build
      
    - name: Deploy
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./out
```

## Optimizaciones para Producción

### 1. Optimización de Imágenes
```javascript
// next.config.js
module.exports = {
  images: {
    domains: ['example.com'],
    formats: ['image/webp', 'image/avif'],
  },
}
```

### 2. Compresión
```bash
# Instalar plugin de compresión
npm install next-pwa

# Configurar en next.config.js
const withPWA = require('next-pwa')({
  dest: 'public'
})

module.exports = withPWA({
  // tu configuración
})
```

### 3. Analytics
```javascript
// Agregar Google Analytics
export default function RootLayout({ children }) {
  return (
    <html>
      <head>
        <script
          async
          src={`https://www.googletagmanager.com/gtag/js?id=${GA_TRACKING_ID}`}
        />
        <script
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', '${GA_TRACKING_ID}');
            `,
          }}
        />
      </head>
      <body>{children}</body>
    </html>
  )
}
```

## Monitoreo y Mantenimiento

### 1. Lighthouse CI
```yaml
# .github/workflows/lighthouse.yml
name: Lighthouse CI
on: [push]
jobs:
  lighthouse:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Audit URLs using Lighthouse CI
        uses: treosh/lighthouse-ci-action@v3
        with:
          urls: |
            https://tu-proyecto.vercel.app
          uploadArtifacts: true
```

### 2. Error Tracking
```bash
# Instalar Sentry
npm install @sentry/nextjs

# Configurar en next.config.js
const { withSentryConfig } = require('@sentry/nextjs')

module.exports = withSentryConfig({
  // tu configuración
})
```

## Troubleshooting

### Error: "Module not found"
```bash
# Limpiar cache y reinstalar
rm -rf .next node_modules package-lock.json
npm install
npm run build
```

### Error: "Build failed"
```bash
# Verificar logs de build
npm run build -- --debug

# Verificar dependencias
npm audit
npm audit fix
```

### Error: "Runtime error"
```bash
# Verificar variables de entorno
echo $NODE_ENV

# Verificar configuración
npm run lint
```

---

¡Tu bosque 3D estará listo para que todo el mundo lo explore! 🌲✨
