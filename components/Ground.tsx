'use client'

import { useMemo } from 'react'
import * as THREE from 'three'

export default function Ground() {
  // Geometría del suelo
  const groundGeometry = useMemo(() => new THREE.PlaneGeometry(100, 100, 50, 50), [])

  // Material del suelo con textura procedural
  const groundMaterial = useMemo(() => {
    const material = new THREE.MeshLambertMaterial({
      color: '#2d5a3d',
      transparent: true,
      opacity: 0.9
    })
    return material
  }, [])

  // Crear variaciones en el terreno
  useMemo(() => {
    const vertices = groundGeometry.attributes.position.array as Float32Array
    for (let i = 0; i < vertices.length; i += 3) {
      // Añadir variación en Y (altura)
      vertices[i + 2] += (Math.random() - 0.5) * 0.3
    }
    groundGeometry.attributes.position.needsUpdate = true
    groundGeometry.computeVertexNormals()
  }, [groundGeometry])

  return (
    <group>
      {/* Suelo principal */}
      <mesh
        geometry={groundGeometry}
        material={groundMaterial}
        rotation={[-Math.PI / 2, 0, 0]}
        position={[0, 0, 0]}
        receiveShadow
      />

      {/* Círculos de hierba */}
      {Array.from({ length: 200 }, (_, i) => {
        const x = (Math.random() - 0.5) * 80
        const z = (Math.random() - 0.5) * 80
        const scale = 0.1 + Math.random() * 0.2
        
        return (
          <mesh
            key={i}
            position={[x, 0.01, z]}
            rotation={[-Math.PI / 2, 0, Math.random() * Math.PI]}
            scale={scale}
          >
            <circleGeometry args={[1, 6]} />
            <meshLambertMaterial 
              color={new THREE.Color().setHSL(0.25, 0.8, 0.4 + Math.random() * 0.2)}
              transparent
              opacity={0.7}
            />
          </mesh>
        )
      })}

      {/* Rocas esparcidas */}
      {Array.from({ length: 30 }, (_, i) => {
        const x = (Math.random() - 0.5) * 60
        const z = (Math.random() - 0.5) * 60
        const scale = 0.2 + Math.random() * 0.4
        
        return (
          <mesh
            key={`rock-${i}`}
            position={[x, scale * 0.3, z]}
            rotation={[
              Math.random() * 0.3,
              Math.random() * Math.PI,
              Math.random() * 0.3
            ]}
            scale={scale}
            castShadow
            receiveShadow
          >
            <dodecahedronGeometry args={[1, 0]} />
            <meshLambertMaterial 
              color={new THREE.Color().setHSL(0, 0, 0.3 + Math.random() * 0.2)}
            />
          </mesh>
        )
      })}

      {/* Sendero */}
      <mesh
        position={[0, 0.005, 0]}
        rotation={[-Math.PI / 2, 0, 0]}
      >
        <planeGeometry args={[2, 40]} />
        <meshLambertMaterial 
          color="#8b7355"
          transparent
          opacity={0.8}
        />
      </mesh>
    </group>
  )
}
