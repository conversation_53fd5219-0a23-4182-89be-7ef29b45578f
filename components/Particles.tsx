'use client'

import { useRef, useMemo } from 'react'
import { useFrame } from '@react-three/fiber'
import * as THREE from 'three'

export default function Particles() {
  const particlesRef = useRef<THREE.Points>(null)
  const particleCount = 1000

  // Crear geometría y posiciones de partículas
  const { geometry, positions, velocities } = useMemo(() => {
    const geometry = new THREE.BufferGeometry()
    const positions = new Float32Array(particleCount * 3)
    const velocities = new Float32Array(particleCount * 3)

    for (let i = 0; i < particleCount; i++) {
      const i3 = i * 3
      
      // Posiciones aleatorias en el bosque
      positions[i3] = (Math.random() - 0.5) * 50
      positions[i3 + 1] = Math.random() * 10
      positions[i3 + 2] = (Math.random() - 0.5) * 50

      // Velocidades aleatorias
      velocities[i3] = (Math.random() - 0.5) * 0.02
      velocities[i3 + 1] = Math.random() * 0.01
      velocities[i3 + 2] = (Math.random() - 0.5) * 0.02
    }

    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3))
    
    return { geometry, positions, velocities }
  }, [particleCount])

  // Material de partículas
  const material = useMemo(() => {
    return new THREE.PointsMaterial({
      color: '#ffffff',
      size: 0.05,
      transparent: true,
      opacity: 0.6,
      blending: THREE.AdditiveBlending,
      sizeAttenuation: true
    })
  }, [])

  // Animación de partículas
  useFrame((state) => {
    if (particlesRef.current) {
      const positions = particlesRef.current.geometry.attributes.position.array as Float32Array

      for (let i = 0; i < particleCount; i++) {
        const i3 = i * 3

        // Movimiento de partículas
        positions[i3] += velocities[i3]
        positions[i3 + 1] += velocities[i3 + 1]
        positions[i3 + 2] += velocities[i3 + 2]

        // Efecto de viento
        positions[i3] += Math.sin(state.clock.elapsedTime * 0.5 + i * 0.01) * 0.001
        positions[i3 + 1] += Math.cos(state.clock.elapsedTime * 0.3 + i * 0.01) * 0.001

        // Resetear partículas que salen del área
        if (positions[i3] > 25) positions[i3] = -25
        if (positions[i3] < -25) positions[i3] = 25
        if (positions[i3 + 1] > 10) positions[i3 + 1] = 0
        if (positions[i3 + 2] > 25) positions[i3 + 2] = -25
        if (positions[i3 + 2] < -25) positions[i3 + 2] = 25
      }

      particlesRef.current.geometry.attributes.position.needsUpdate = true
    }
  })

  return (
    <points ref={particlesRef} geometry={geometry} material={material} />
  )
}
