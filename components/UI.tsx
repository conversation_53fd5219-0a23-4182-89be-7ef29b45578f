'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

interface UIProps {
  onToggleStats: () => void
  showStats: boolean
}

export default function UI({ onToggleStats, showStats }: UIProps) {
  const [showControls, setShowControls] = useState(false)
  const [showInfo, setShowInfo] = useState(false)

  return (
    <div className="ui-overlay">
      {/* Header */}
      <motion.header
        className="absolute top-0 left-0 right-0 p-6 z-50"
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.8, delay: 0.5 }}
      >
        <div className="flex justify-between items-center">
          <div className="glass-panel">
            <h1 className="text-2xl font-bold text-white">
              🌲 Vale por un Bosque 3D
            </h1>
            <p className="text-green-200 text-sm">
              Explora la naturaleza virtual
            </p>
          </div>

          <div className="flex space-x-4">
            <button
              onClick={() => setShowControls(!showControls)}
              className="nature-button"
            >
              ⚙️ Controles
            </button>
            <button
              onClick={() => setShowInfo(!showInfo)}
              className="nature-button"
            >
              ℹ️ Info
            </button>
          </div>
        </div>
      </motion.header>

      {/* Panel de controles */}
      <AnimatePresence>
        {showControls && (
          <motion.div
            className="absolute top-24 right-6 glass-panel w-80 z-40"
            initial={{ opacity: 0, x: 100 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 100 }}
            transition={{ duration: 0.3 }}
          >
            <h3 className="text-lg font-semibold text-white mb-4">
              🎮 Controles
            </h3>
            
            <div className="space-y-3 text-sm text-green-100">
              <div className="flex justify-between">
                <span>🖱️ Rotar cámara:</span>
                <span>Click + Arrastrar</span>
              </div>
              <div className="flex justify-between">
                <span>🔍 Zoom:</span>
                <span>Rueda del ratón</span>
              </div>
              <div className="flex justify-between">
                <span>📱 Mover:</span>
                <span>Click derecho + Arrastrar</span>
              </div>
            </div>

            <div className="mt-4 pt-4 border-t border-white/20">
              <button
                onClick={onToggleStats}
                className={`w-full py-2 px-4 rounded-lg transition-colors ${
                  showStats 
                    ? 'bg-red-500 hover:bg-red-600' 
                    : 'bg-green-500 hover:bg-green-600'
                }`}
              >
                {showStats ? '📊 Ocultar Stats' : '📊 Mostrar Stats'}
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Panel de información */}
      <AnimatePresence>
        {showInfo && (
          <motion.div
            className="absolute top-24 left-6 glass-panel w-80 z-40"
            initial={{ opacity: 0, x: -100 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -100 }}
            transition={{ duration: 0.3 }}
          >
            <h3 className="text-lg font-semibold text-white mb-4">
              🌿 Sobre el Bosque
            </h3>
            
            <div className="space-y-3 text-sm text-green-100">
              <p>
                Bienvenido a una experiencia inmersiva en un bosque virtual 
                creado con tecnología 3D avanzada.
              </p>
              
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <span>🌲</span>
                  <span>150+ árboles únicos</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span>🦋</span>
                  <span>Mariposas animadas</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span>🐦</span>
                  <span>Pájaros volando</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span>✨</span>
                  <span>Partículas ambientales</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span>🌅</span>
                  <span>Iluminación dinámica</span>
                </div>
              </div>

              <div className="mt-4 pt-4 border-t border-white/20">
                <p className="text-xs text-green-200">
                  Desarrollado con Three.js, React y Next.js
                </p>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Footer */}
      <motion.footer
        className="absolute bottom-0 left-0 right-0 p-6 z-50"
        initial={{ y: 100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.8, delay: 0.7 }}
      >
        <div className="flex justify-between items-center">
          <div className="glass-panel">
            <p className="text-green-200 text-sm">
              🎵 Sonidos de la naturaleza activados
            </p>
          </div>

          <div className="glass-panel">
            <p className="text-green-200 text-sm">
              Presiona F11 para pantalla completa
            </p>
          </div>
        </div>
      </motion.footer>

      {/* Indicador de carga en tiempo real */}
      <motion.div
        className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 2 }}
      >
        <div className="text-center">
          <motion.div
            className="text-6xl mb-4"
            animate={{ 
              scale: [1, 1.1, 1],
              rotate: [0, 5, -5, 0]
            }}
            transition={{ 
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            🌲
          </motion.div>
          <p className="text-white/70 text-sm">
            Disfruta explorando el bosque
          </p>
        </div>
      </motion.div>
    </div>
  )
}
