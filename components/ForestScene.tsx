'use client'

import { useRef, useMemo } from 'react'
import { useFrame } from '@react-three/fiber'
import { Instances, Instance, useGLTF, Text3D, Center } from '@react-three/drei'
import * as THREE from 'three'
import Tree from './Tree'
import Ground from './Ground'
import Particles from './Particles'
import Animals from './Animals'

export default function ForestScene() {
  const groupRef = useRef<THREE.Group>(null)

  // Generar posiciones aleatorias para los árboles
  const treePositions = useMemo(() => {
    const positions = []
    const gridSize = 20
    const treeCount = 150

    for (let i = 0; i < treeCount; i++) {
      const x = (Math.random() - 0.5) * gridSize * 2
      const z = (Math.random() - 0.5) * gridSize * 2
      const y = 0
      
      // Evitar colocar árboles muy cerca del centro
      if (Math.sqrt(x * x + z * z) > 3) {
        positions.push({
          position: [x, y, z] as [number, number, number],
          rotation: [0, Math.random() * Math.PI * 2, 0] as [number, number, number],
          scale: 0.8 + Math.random() * 0.4
        })
      }
    }
    return positions
  }, [])

  // Animación sutil del viento
  useFrame((state) => {
    if (groupRef.current) {
      groupRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.1) * 0.01
    }
  })

  return (
    <group ref={groupRef}>
      {/* Suelo del bosque */}
      <Ground />

      {/* Árboles */}
      <Instances limit={treePositions.length}>
        <Tree />
        {treePositions.map((tree, index) => (
          <Instance
            key={index}
            position={tree.position}
            rotation={tree.rotation}
            scale={tree.scale}
          />
        ))}
      </Instances>

      {/* Partículas ambientales */}
      <Particles />

      {/* Animales del bosque */}
      <Animals />

      {/* Texto de bienvenida */}
      <Center position={[0, 3, -8]}>
        <Text3D
          font="/fonts/Inter_Bold.json"
          size={0.8}
          height={0.1}
          curveSegments={12}
          bevelEnabled
          bevelThickness={0.02}
          bevelSize={0.02}
          bevelOffset={0}
          bevelSegments={5}
        >
          Vale por un Bosque 3D
          <meshStandardMaterial 
            color="#4ade80" 
            emissive="#22c55e"
            emissiveIntensity={0.2}
          />
        </Text3D>
      </Center>

      {/* Luces adicionales */}
      <pointLight
        position={[5, 5, 5]}
        intensity={0.5}
        color="#ffd700"
        distance={20}
        decay={2}
      />
      
      <pointLight
        position={[-5, 3, -5]}
        intensity={0.3}
        color="#87ceeb"
        distance={15}
        decay={2}
      />

      {/* Niebla volumétrica */}
      <fog attach="fog" args={['#2d5a3d', 10, 50]} />
    </group>
  )
}
