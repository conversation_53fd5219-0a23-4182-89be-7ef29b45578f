'use client'

import { useEffect, useRef, useState } from 'react'

export default function AudioManager() {
  const audioRef = useRef<HTMLAudioElement>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [volume, setVolume] = useState(0.3)

  useEffect(() => {
    // Crear contexto de audio para sonidos ambientales
    const createAmbientSounds = () => {
      if (typeof window !== 'undefined' && 'AudioContext' in window) {
        const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
        
        // Función para crear sonido de viento
        const createWindSound = () => {
          const oscillator = audioContext.createOscillator()
          const gainNode = audioContext.createGain()
          const filter = audioContext.createBiquadFilter()

          oscillator.type = 'sawtooth'
          oscillator.frequency.setValueAtTime(80, audioContext.currentTime)
          
          filter.type = 'lowpass'
          filter.frequency.setValueAtTime(200, audioContext.currentTime)
          filter.Q.setValueAtTime(1, audioContext.currentTime)

          gainNode.gain.setValueAtTime(0, audioContext.currentTime)
          gainNode.gain.linearRampToValueAtTime(volume * 0.1, audioContext.currentTime + 2)

          oscillator.connect(filter)
          filter.connect(gainNode)
          gainNode.connect(audioContext.destination)

          oscillator.start()
          
          // Variación en el tiempo
          setInterval(() => {
            if (oscillator.frequency) {
              oscillator.frequency.setValueAtTime(
                80 + Math.sin(Date.now() * 0.001) * 20,
                audioContext.currentTime
              )
            }
          }, 100)

          return { oscillator, gainNode }
        }

        // Función para crear sonidos de pájaros
        const createBirdSound = () => {
          const oscillator = audioContext.createOscillator()
          const gainNode = audioContext.createGain()

          oscillator.type = 'sine'
          oscillator.frequency.setValueAtTime(800, audioContext.currentTime)
          
          gainNode.gain.setValueAtTime(0, audioContext.currentTime)

          oscillator.connect(gainNode)
          gainNode.connect(audioContext.destination)

          // Patrón de canto de pájaro
          const playBirdChirp = () => {
            const now = audioContext.currentTime
            oscillator.frequency.setValueAtTime(800, now)
            oscillator.frequency.linearRampToValueAtTime(1200, now + 0.1)
            oscillator.frequency.linearRampToValueAtTime(600, now + 0.2)
            
            gainNode.gain.setValueAtTime(0, now)
            gainNode.gain.linearRampToValueAtTime(volume * 0.05, now + 0.05)
            gainNode.gain.linearRampToValueAtTime(0, now + 0.2)
          }

          oscillator.start()

          // Reproducir chirp aleatoriamente
          const birdInterval = setInterval(() => {
            if (Math.random() < 0.1) { // 10% de probabilidad cada segundo
              playBirdChirp()
            }
          }, 1000)

          return { oscillator, gainNode, interval: birdInterval }
        }

        if (isPlaying) {
          const windSound = createWindSound()
          const birdSound = createBirdSound()

          return () => {
            windSound.oscillator.stop()
            birdSound.oscillator.stop()
            clearInterval(birdSound.interval)
          }
        }
      }
    }

    const cleanup = createAmbientSounds()
    return cleanup
  }, [isPlaying, volume])

  const toggleAudio = () => {
    setIsPlaying(!isPlaying)
  }

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(e.target.value)
    setVolume(newVolume)
  }

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <div className="glass-panel flex items-center space-x-4">
        <button
          onClick={toggleAudio}
          className="nature-button flex items-center space-x-2"
        >
          <span>{isPlaying ? '🔊' : '🔇'}</span>
          <span>{isPlaying ? 'Pausar' : 'Reproducir'}</span>
        </button>

        {isPlaying && (
          <div className="flex items-center space-x-2">
            <span className="text-white text-sm">🎵</span>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={volume}
              onChange={handleVolumeChange}
              className="w-20 h-2 bg-white/20 rounded-lg appearance-none cursor-pointer"
              style={{
                background: `linear-gradient(to right, #4ade80 0%, #4ade80 ${volume * 100}%, rgba(255,255,255,0.2) ${volume * 100}%, rgba(255,255,255,0.2) 100%)`
              }}
            />
          </div>
        )}
      </div>

      {/* Indicador visual de audio */}
      {isPlaying && (
        <div className="absolute -top-2 -right-2">
          <div className="w-4 h-4 bg-green-500 rounded-full animate-pulse"></div>
        </div>
      )}
    </div>
  )
}
