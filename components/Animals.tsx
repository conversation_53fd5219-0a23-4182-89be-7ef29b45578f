'use client'

import { useRef, useMemo } from 'react'
import { useFrame } from '@react-three/fiber'
import * as THREE from 'three'

// Componente para un pájaro simple
function Bird({ position, color }: { position: [number, number, number], color: string }) {
  const birdRef = useRef<THREE.Group>(null)
  const speed = useMemo(() => 0.5 + Math.random() * 0.5, [])
  const radius = useMemo(() => 5 + Math.random() * 10, [])
  const offset = useMemo(() => Math.random() * Math.PI * 2, [])

  useFrame((state) => {
    if (birdRef.current) {
      const time = state.clock.elapsedTime * speed + offset
      birdRef.current.position.x = position[0] + Math.cos(time) * radius
      birdRef.current.position.y = position[1] + Math.sin(time * 0.5) * 2
      birdRef.current.position.z = position[2] + Math.sin(time) * radius
      
      // Orientar el pájaro hacia la dirección de movimiento
      birdRef.current.lookAt(
        birdRef.current.position.x + Math.cos(time + 0.1),
        birdRef.current.position.y,
        birdRef.current.position.z + Math.sin(time + 0.1)
      )
    }
  })

  return (
    <group ref={birdRef} position={position}>
      {/* Cuerpo del pájaro */}
      <mesh position={[0, 0, 0]} scale={0.1}>
        <sphereGeometry args={[1, 6, 4]} />
        <meshLambertMaterial color={color} />
      </mesh>
      
      {/* Alas */}
      <mesh position={[-0.08, 0, 0]} rotation={[0, 0, Math.PI / 4]} scale={0.05}>
        <boxGeometry args={[2, 0.2, 0.8]} />
        <meshLambertMaterial color={color} />
      </mesh>
      <mesh position={[0.08, 0, 0]} rotation={[0, 0, -Math.PI / 4]} scale={0.05}>
        <boxGeometry args={[2, 0.2, 0.8]} />
        <meshLambertMaterial color={color} />
      </mesh>
    </group>
  )
}

// Componente para una mariposa
function Butterfly({ position }: { position: [number, number, number] }) {
  const butterflyRef = useRef<THREE.Group>(null)
  const speed = useMemo(() => 0.3 + Math.random() * 0.3, [])
  const path = useMemo(() => {
    return Array.from({ length: 20 }, (_, i) => ({
      x: position[0] + (Math.random() - 0.5) * 10,
      y: position[1] + Math.random() * 3,
      z: position[2] + (Math.random() - 0.5) * 10
    }))
  }, [position])
  
  const pathIndex = useRef(0)

  useFrame((state) => {
    if (butterflyRef.current) {
      const time = state.clock.elapsedTime * speed
      const currentTarget = path[Math.floor(time) % path.length]
      
      // Movimiento suave hacia el siguiente punto
      butterflyRef.current.position.lerp(
        new THREE.Vector3(currentTarget.x, currentTarget.y, currentTarget.z),
        0.02
      )
      
      // Aleteo de las alas
      butterflyRef.current.children.forEach((wing, index) => {
        if (wing instanceof THREE.Mesh) {
          wing.rotation.y = Math.sin(time * 10) * 0.3 * (index % 2 === 0 ? 1 : -1)
        }
      })
    }
  })

  return (
    <group ref={butterflyRef} position={position}>
      {/* Cuerpo */}
      <mesh scale={0.02}>
        <cylinderGeometry args={[0.5, 0.5, 4, 4]} />
        <meshLambertMaterial color="#4a4a4a" />
      </mesh>
      
      {/* Alas */}
      <mesh position={[-0.03, 0, 0]} scale={0.02}>
        <sphereGeometry args={[2, 6, 4]} />
        <meshLambertMaterial color="#ff6b6b" transparent opacity={0.8} />
      </mesh>
      <mesh position={[0.03, 0, 0]} scale={0.02}>
        <sphereGeometry args={[2, 6, 4]} />
        <meshLambertMaterial color="#4ecdc4" transparent opacity={0.8} />
      </mesh>
    </group>
  )
}

export default function Animals() {
  // Posiciones de los pájaros
  const birdPositions = useMemo(() => [
    { position: [10, 8, 5] as [number, number, number], color: '#ff6b6b' },
    { position: [-8, 12, -3] as [number, number, number], color: '#4ecdc4' },
    { position: [5, 15, -10] as [number, number, number], color: '#45b7d1' },
    { position: [-12, 10, 8] as [number, number, number], color: '#f9ca24' },
  ], [])

  // Posiciones de las mariposas
  const butterflyPositions = useMemo(() => [
    [3, 2, 2] as [number, number, number],
    [-5, 1.5, -4] as [number, number, number],
    [8, 2.5, -1] as [number, number, number],
    [-2, 1.8, 6] as [number, number, number],
    [12, 2.2, 3] as [number, number, number],
  ], [])

  return (
    <group>
      {/* Pájaros volando */}
      {birdPositions.map((bird, index) => (
        <Bird
          key={`bird-${index}`}
          position={bird.position}
          color={bird.color}
        />
      ))}

      {/* Mariposas */}
      {butterflyPositions.map((position, index) => (
        <Butterfly
          key={`butterfly-${index}`}
          position={position}
        />
      ))}
    </group>
  )
}
