'use client'

import { motion } from 'framer-motion'

export default function LoadingScreen() {
  return (
    <motion.div
      className="loading-screen"
      initial={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="flex flex-col items-center justify-center space-y-8">
        {/* Logo/Título */}
        <motion.h1
          className="text-4xl md:text-6xl font-bold text-white text-center"
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          Vale por un Bosque 3D
        </motion.h1>

        {/* Subtítulo */}
        <motion.p
          className="text-lg md:text-xl text-green-200 text-center max-w-md"
          initial={{ y: 30, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          Preparando tu experiencia inmersiva en la naturaleza virtual
        </motion.p>

        {/* Spinner de carga */}
        <motion.div
          className="loading-spinner"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        />

        {/* Barra de progreso animada */}
        <motion.div
          className="w-64 h-2 bg-white/20 rounded-full overflow-hidden"
          initial={{ width: 0 }}
          animate={{ width: 256 }}
          transition={{ duration: 0.8, delay: 0.8 }}
        >
          <motion.div
            className="h-full bg-gradient-to-r from-green-400 to-green-600 rounded-full"
            initial={{ width: "0%" }}
            animate={{ width: "100%" }}
            transition={{ duration: 1.5, delay: 1 }}
          />
        </motion.div>

        {/* Texto de carga */}
        <motion.p
          className="text-sm text-green-300"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 1.2 }}
        >
          Cargando árboles, animales y sonidos del bosque...
        </motion.p>

        {/* Iconos flotantes */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {Array.from({ length: 20 }, (_, i) => (
            <motion.div
              key={i}
              className="absolute text-green-300/30 text-2xl"
              initial={{
                x: Math.random() * window.innerWidth,
                y: window.innerHeight + 50,
                rotate: 0,
              }}
              animate={{
                y: -50,
                rotate: 360,
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                delay: Math.random() * 2,
                repeat: Infinity,
                ease: "linear",
              }}
            >
              {['🌲', '🦋', '🐦', '🍃', '🌿'][Math.floor(Math.random() * 5)]}
            </motion.div>
          ))}
        </div>
      </div>
    </motion.div>
  )
}
