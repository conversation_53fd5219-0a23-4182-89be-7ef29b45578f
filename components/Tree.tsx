'use client'

import { useRef, useMemo } from 'react'
import { useFrame } from '@react-three/fiber'
import * as THREE from 'three'

export default function Tree() {
  const trunkRef = useRef<THREE.Mesh>(null)
  const leavesRef = useRef<THREE.Mesh>(null)

  // Geometrías optimizadas
  const trunkGeometry = useMemo(() => new THREE.CylinderGeometry(0.1, 0.15, 2, 8), [])
  const leavesGeometry = useMemo(() => new THREE.SphereGeometry(1.2, 8, 6), [])

  // Materiales con variaciones
  const trunkMaterial = useMemo(() => new THREE.MeshLambertMaterial({ 
    color: new THREE.Color().setHSL(0.08, 0.6, 0.3 + Math.random() * 0.2)
  }), [])
  
  const leavesMaterial = useMemo(() => new THREE.MeshLambertMaterial({ 
    color: new THREE.Color().setHSL(0.25, 0.7, 0.3 + Math.random() * 0.3)
  }), [])

  // Animación sutil del viento
  useFrame((state) => {
    if (leavesRef.current) {
      leavesRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.05
      leavesRef.current.position.x = Math.sin(state.clock.elapsedTime * 0.3) * 0.02
    }
    if (trunkRef.current) {
      trunkRef.current.rotation.z = Math.sin(state.clock.elapsedTime * 0.2) * 0.01
    }
  })

  return (
    <group>
      {/* Tronco */}
      <mesh
        ref={trunkRef}
        geometry={trunkGeometry}
        material={trunkMaterial}
        position={[0, 1, 0]}
        castShadow
        receiveShadow
      />
      
      {/* Hojas */}
      <mesh
        ref={leavesRef}
        geometry={leavesGeometry}
        material={leavesMaterial}
        position={[0, 2.5, 0]}
        castShadow
      />

      {/* Hojas adicionales para más volumen */}
      <mesh
        geometry={leavesGeometry}
        material={leavesMaterial}
        position={[0.3, 2.2, 0.2]}
        scale={0.7}
        castShadow
      />
      
      <mesh
        geometry={leavesGeometry}
        material={leavesMaterial}
        position={[-0.2, 2.8, -0.3]}
        scale={0.5}
        castShadow
      />
    </group>
  )
}
