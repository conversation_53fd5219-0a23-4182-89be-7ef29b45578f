"""
Signature model for storing signature images and metadata.
"""
from sqlalchemy import Column, String, Text, <PERSON>olean, Foreign<PERSON>ey, Integer, Float
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class Signature(BaseModel):
    """
    Signature model for storing signature images and configuration.
    """
    __tablename__ = "signatures"
    
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    image_path = Column(String(500), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Signature properties
    width = Column(Float, nullable=False, default=100.0)  # Width in points
    height = Column(Float, nullable=False, default=50.0)  # Height in points
    transparency = Column(Float, nullable=False, default=1.0)  # 0.0 to 1.0
    
    # Foreign key to department
    department_id = Column(Integer, ForeignKey("departments.id"), nullable=False)
    
    # Relationships
    department = relationship("Department", back_populates="signatures")
    positions = relationship("SignaturePosition", back_populates="signature")
    
    def __repr__(self):
        return f"<Signature(id={self.id}, name='{self.name}', department_id={self.department_id})>"
