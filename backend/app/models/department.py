"""
Department model for organizing users and signatures.
"""
from sqlalchemy import Column, String, Text, Boolean
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class Department(BaseModel):
    """
    Department model for organizing users and their signatures.
    """
    __tablename__ = "departments"
    
    name = Column(String(255), unique=True, nullable=False, index=True)
    description = Column(Text, nullable=True)
    code = Column(String(10), unique=True, nullable=False, index=True)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Relationships
    users = relationship("User", back_populates="department")
    signatures = relationship("Signature", back_populates="department")
    
    def __repr__(self):
        return f"<Department(id={self.id}, name='{self.name}', code='{self.code}')>"
