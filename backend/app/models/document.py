"""
Document model for tracking PDF documents and their processing status.
"""
from enum import Enum
from sqlalchemy import Column, String, Text, <PERSON>ole<PERSON>, Integer, Enum as <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Float
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class DocumentStatus(str, Enum):
    """Document processing status."""
    UPLOADED = "uploaded"
    PROCESSING = "processing"
    ANALYZED = "analyzed"
    SIGNED = "signed"
    ERROR = "error"


class Document(BaseModel):
    """
    Document model for tracking PDF documents through the signature process.
    """
    __tablename__ = "documents"
    
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=False)
    mime_type = Column(String(100), nullable=False, default="application/pdf")
    
    # Document metadata
    title = Column(String(500), nullable=True)
    author = Column(String(255), nullable=True)
    subject = Column(String(500), nullable=True)
    keywords = Column(Text, nullable=True)
    
    # Processing status
    status = Column(SQLEnum(DocumentStatus), default=DocumentStatus.UPLOADED, nullable=False)
    error_message = Column(Text, nullable=True)
    
    # Document analysis results
    page_count = Column(Integer, nullable=True)
    text_content = Column(Text, nullable=True)
    detected_department = Column(String(255), nullable=True)
    confidence_score = Column(Float, nullable=True)
    
    # Foreign key to user who uploaded
    uploaded_by_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Foreign key to assigned department
    department_id = Column(Integer, ForeignKey("departments.id"), nullable=True)
    
    # Relationships
    uploaded_by = relationship("User")
    department = relationship("Department")
    signature_positions = relationship("SignaturePosition", back_populates="document")
    
    def __repr__(self):
        return f"<Document(id={self.id}, filename='{self.filename}', status='{self.status}')>"
