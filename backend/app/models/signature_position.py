"""
Signature position model for tracking where signatures should be placed in documents.
"""
from sqlalchemy import <PERSON>umn, Integer, Float, Boolean, ForeignKey
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class SignaturePosition(BaseModel):
    """
    Model for storing signature positions within documents.
    """
    __tablename__ = "signature_positions"
    
    # Position coordinates (in points from bottom-left corner)
    x = Column(Float, nullable=False)
    y = Column(Float, nullable=False)
    page_number = Column(Integer, nullable=False, default=1)
    
    # Signature dimensions at this position
    width = Column(Float, nullable=False)
    height = Column(Float, nullable=False)
    
    # Position metadata
    is_applied = Column(Boolean, default=False, nullable=False)
    auto_detected = Column(Boolean, default=False, nullable=False)
    confidence_score = Column(Float, nullable=True)
    
    # Foreign keys
    document_id = Column(Integer, ForeignKey("documents.id"), nullable=False)
    signature_id = Column(In<PERSON>ger, Foreign<PERSON>ey("signatures.id"), nullable=False)
    
    # Relationships
    document = relationship("Document", back_populates="signature_positions")
    signature = relationship("Signature", back_populates="positions")
    
    def __repr__(self):
        return f"<SignaturePosition(id={self.id}, x={self.x}, y={self.y}, page={self.page_number})>"
