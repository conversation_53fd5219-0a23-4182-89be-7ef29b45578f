"""
User service for user management operations.
"""
from typing import Optional
from sqlalchemy.orm import Session
from fastapi import HTTPException, status

from app.models.user import User
from app.schemas.user import UserCreate, UserUpdate
from app.services.base_service import BaseService
from app.core.security import get_password_hash, verify_password


class UserService(BaseService[User, UserCreate, UserUpdate]):
    """
    User service with authentication methods.
    """
    
    def __init__(self, db: Session):
        super().__init__(User, db)
    
    def get_by_email(self, *, email: str) -> Optional[User]:
        """
        Get user by email address.
        
        Args:
            email: User email address
            
        Returns:
            User instance or None
        """
        return self.db.query(User).filter(User.email == email).first()
    
    def get_by_username(self, *, username: str) -> Optional[User]:
        """
        Get user by username.
        
        Args:
            username: Username
            
        Returns:
            User instance or None
        """
        return self.db.query(User).filter(User.username == username).first()
    
    def create(self, *, obj_in: UserCreate) -> User:
        """
        Create a new user with hashed password.
        
        Args:
            obj_in: User creation schema
            
        Returns:
            Created user instance
            
        Raises:
            HTTPException: If email or username already exists
        """
        # Check if email already exists
        if self.get_by_email(email=obj_in.email):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )
        
        # Check if username already exists
        if self.get_by_username(username=obj_in.username):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already taken"
            )
        
        # Create user with hashed password
        obj_in_data = obj_in.model_dump()
        del obj_in_data["password"]
        obj_in_data["hashed_password"] = get_password_hash(obj_in.password)
        
        db_obj = User(**obj_in_data)
        self.db.add(db_obj)
        self.db.commit()
        self.db.refresh(db_obj)
        return db_obj
    
    def authenticate(self, *, email: str, password: str) -> Optional[User]:
        """
        Authenticate user with email and password.
        
        Args:
            email: User email
            password: Plain text password
            
        Returns:
            User instance if authentication successful, None otherwise
        """
        user = self.get_by_email(email=email)
        if not user:
            return None
        if not verify_password(password, user.hashed_password):
            return None
        return user
    
    def is_active(self, user: User) -> bool:
        """
        Check if user is active.
        
        Args:
            user: User instance
            
        Returns:
            True if user is active
        """
        return user.is_active
    
    def is_superuser(self, user: User) -> bool:
        """
        Check if user is superuser.
        
        Args:
            user: User instance
            
        Returns:
            True if user is superuser
        """
        return user.is_superuser
