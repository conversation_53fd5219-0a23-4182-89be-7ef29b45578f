"""
Signature position management endpoints.
"""
from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.api.api_v1.endpoints.auth import get_current_user
from app.services.base_service import BaseService
from app.models.signature_position import SignaturePosition
from app.schemas.signature_position import SignaturePosition as SignaturePositionSchema, SignaturePositionCreate, SignaturePositionUpdate
from app.schemas.user import User

router = APIRouter()


@router.get("/", response_model=List[SignaturePositionSchema])
def read_signature_positions(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    document_id: int = None,
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Retrieve signature positions.
    """
    service = BaseService(SignaturePosition, db)
    filters = {}
    if document_id:
        filters["document_id"] = document_id
    positions = service.get_multi(skip=skip, limit=limit, **filters)
    return positions


@router.post("/", response_model=SignaturePositionSchema)
def create_signature_position(
    *,
    db: Session = Depends(get_db),
    position_in: SignaturePositionCreate,
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Create new signature position.
    """
    service = BaseService(SignaturePosition, db)
    position = service.create(obj_in=position_in)
    return position


@router.get("/{position_id}", response_model=SignaturePositionSchema)
def read_signature_position(
    position_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Get a specific signature position by id.
    """
    service = BaseService(SignaturePosition, db)
    position = service.get(id=position_id)
    if not position:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Signature position not found"
        )
    return position


@router.put("/{position_id}", response_model=SignaturePositionSchema)
def update_signature_position(
    *,
    db: Session = Depends(get_db),
    position_id: int,
    position_in: SignaturePositionUpdate,
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Update a signature position.
    """
    service = BaseService(SignaturePosition, db)
    position = service.get(id=position_id)
    if not position:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Signature position not found"
        )
    position = service.update(db_obj=position, obj_in=position_in)
    return position
