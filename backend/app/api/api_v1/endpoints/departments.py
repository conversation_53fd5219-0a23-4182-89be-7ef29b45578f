"""
Department management endpoints.
"""
from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.api.api_v1.endpoints.auth import get_current_user
from app.services.base_service import BaseService
from app.models.department import Department
from app.schemas.department import Department as DepartmentSchema, DepartmentCreate, DepartmentUpdate
from app.schemas.user import User

router = APIRouter()


@router.get("/", response_model=List[DepartmentSchema])
def read_departments(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Retrieve departments.
    """
    service = BaseService(Department, db)
    departments = service.get_multi(skip=skip, limit=limit)
    return departments


@router.post("/", response_model=DepartmentSchema)
def create_department(
    *,
    db: Session = Depends(get_db),
    department_in: DepartmentCreate,
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Create new department.
    """
    service = BaseService(Department, db)
    department = service.create(obj_in=department_in)
    return department


@router.get("/{department_id}", response_model=DepartmentSchema)
def read_department(
    department_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Get a specific department by id.
    """
    service = BaseService(Department, db)
    department = service.get(id=department_id)
    if not department:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Department not found"
        )
    return department


@router.put("/{department_id}", response_model=DepartmentSchema)
def update_department(
    *,
    db: Session = Depends(get_db),
    department_id: int,
    department_in: DepartmentUpdate,
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Update a department.
    """
    service = BaseService(Department, db)
    department = service.get(id=department_id)
    if not department:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Department not found"
        )
    department = service.update(db_obj=department, obj_in=department_in)
    return department
