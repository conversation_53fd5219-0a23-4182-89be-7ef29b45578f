"""
Signature management endpoints.
"""
from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.api.api_v1.endpoints.auth import get_current_user
from app.services.base_service import BaseService
from app.models.signature import Signature
from app.schemas.signature import Signature as SignatureSchema, SignatureCreate, SignatureUpdate
from app.schemas.user import User

router = APIRouter()


@router.get("/", response_model=List[SignatureSchema])
def read_signatures(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    department_id: int = None,
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Retrieve signatures.
    """
    service = BaseService(Signature, db)
    filters = {}
    if department_id:
        filters["department_id"] = department_id
    signatures = service.get_multi(skip=skip, limit=limit, **filters)
    return signatures


@router.post("/", response_model=SignatureSchema)
def create_signature(
    *,
    db: Session = Depends(get_db),
    signature_in: SignatureCreate,
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Create new signature.
    """
    service = BaseService(Signature, db)
    signature = service.create(obj_in=signature_in)
    return signature


@router.get("/{signature_id}", response_model=SignatureSchema)
def read_signature(
    signature_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Get a specific signature by id.
    """
    service = BaseService(Signature, db)
    signature = service.get(id=signature_id)
    if not signature:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Signature not found"
        )
    return signature


@router.put("/{signature_id}", response_model=SignatureSchema)
def update_signature(
    *,
    db: Session = Depends(get_db),
    signature_id: int,
    signature_in: SignatureUpdate,
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Update a signature.
    """
    service = BaseService(Signature, db)
    signature = service.get(id=signature_id)
    if not signature:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Signature not found"
        )
    signature = service.update(db_obj=signature, obj_in=signature_in)
    return signature
