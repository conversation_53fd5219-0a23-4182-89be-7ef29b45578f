"""
Document management endpoints.
"""
from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.api.api_v1.endpoints.auth import get_current_user
from app.services.base_service import BaseService
from app.models.document import Document
from app.schemas.document import Document as DocumentSchema, DocumentCreate, DocumentUpdate
from app.schemas.user import User

router = APIRouter()


@router.get("/", response_model=List[DocumentSchema])
def read_documents(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Retrieve documents.
    """
    service = BaseService(Document, db)
    documents = service.get_multi(skip=skip, limit=limit)
    return documents


@router.post("/upload")
async def upload_document(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Upload a PDF document.
    """
    # TODO: Implement file upload logic
    # This will be implemented in the PDF processing service
    return {"message": "File upload endpoint - to be implemented"}


@router.get("/{document_id}", response_model=DocumentSchema)
def read_document(
    document_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Get a specific document by id.
    """
    service = BaseService(Document, db)
    document = service.get(id=document_id)
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )
    return document


@router.put("/{document_id}", response_model=DocumentSchema)
def update_document(
    *,
    db: Session = Depends(get_db),
    document_id: int,
    document_in: DocumentUpdate,
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Update a document.
    """
    service = BaseService(Document, db)
    document = service.get(id=document_id)
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )
    document = service.update(db_obj=document, obj_in=document_in)
    return document
