"""
API v1 router configuration.
"""
from fastapi import APIRouter

from app.api.api_v1.endpoints import (
    auth,
    users,
    departments,
    signatures,
    documents,
    signature_positions
)

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(departments.router, prefix="/departments", tags=["departments"])
api_router.include_router(signatures.router, prefix="/signatures", tags=["signatures"])
api_router.include_router(documents.router, prefix="/documents", tags=["documents"])
api_router.include_router(signature_positions.router, prefix="/signature-positions", tags=["signature-positions"])
