"""
FastAPI main application.
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import os

from app.core.config import settings
from app.core.database import create_tables
from app.api.api_v1.api import api_router


def create_application() -> FastAPI:
    """
    Create and configure FastAPI application.
    """
    app = FastAPI(
        title=settings.app_name,
        version=settings.app_version,
        description="Sistema completo para la gestión y aplicación automática de firmas en documentos PDF",
        openapi_url="/openapi.json",
        docs_url="/docs",
        redoc_url="/redoc"
    )

    # Set up CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.backend_cors_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Include API router
    app.include_router(api_router, prefix="/api/v1")

    # Mount static files for uploads
    if not os.path.exists(settings.upload_dir):
        os.makedirs(settings.upload_dir)
    
    app.mount("/uploads", StaticFiles(directory=settings.upload_dir), name="uploads")

    return app


# Create FastAPI app
app = create_application()


@app.on_event("startup")
async def startup_event():
    """
    Application startup event.
    """
    # Create database tables
    create_tables()


@app.get("/")
async def root():
    """
    Root endpoint.
    """
    return {
        "message": "PDF Signature System API",
        "version": settings.app_version,
        "docs": "/docs"
    }


@app.get("/health")
async def health_check():
    """
    Health check endpoint.
    """
    return {"status": "healthy"}
