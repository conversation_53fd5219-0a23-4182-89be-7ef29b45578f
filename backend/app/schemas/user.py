"""
User Pydantic schemas.
"""
from typing import Optional
from pydantic import EmailStr, Field

from app.schemas.base import BaseSchema, BaseSchemaWithId


class UserBase(BaseSchema):
    """Base user schema."""
    email: EmailStr
    username: str = Field(..., min_length=3, max_length=100)
    full_name: str = Field(..., min_length=1, max_length=255)
    is_active: bool = True
    department_id: Optional[int] = None


class UserCreate(UserBase):
    """Schema for creating a user."""
    password: str = Field(..., min_length=8, max_length=100)


class UserUpdate(BaseSchema):
    """Schema for updating a user."""
    email: Optional[EmailStr] = None
    username: Optional[str] = Field(None, min_length=3, max_length=100)
    full_name: Optional[str] = Field(None, min_length=1, max_length=255)
    password: Optional[str] = Field(None, min_length=8, max_length=100)
    is_active: Optional[bool] = None
    department_id: Optional[int] = None


class User(UserBase, BaseSchemaWithId):
    """Schema for returning user data."""
    is_superuser: bool


class UserInDB(User):
    """Schema for user data stored in database."""
    hashed_password: str
