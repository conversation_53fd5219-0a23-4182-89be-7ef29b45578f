"""
Signature Pydantic schemas.
"""
from typing import Optional
from pydantic import Field

from app.schemas.base import BaseSchema, BaseSchemaWithId


class SignatureBase(BaseSchema):
    """Base signature schema."""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    width: float = Field(..., gt=0, le=1000)
    height: float = Field(..., gt=0, le=1000)
    transparency: float = Field(..., ge=0.0, le=1.0)
    is_active: bool = True
    department_id: int


class SignatureCreate(SignatureBase):
    """Schema for creating a signature."""
    pass


class SignatureUpdate(BaseSchema):
    """Schema for updating a signature."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    width: Optional[float] = Field(None, gt=0, le=1000)
    height: Optional[float] = Field(None, gt=0, le=1000)
    transparency: Optional[float] = Field(None, ge=0.0, le=1.0)
    is_active: Optional[bool] = None
    department_id: Optional[int] = None


class Signature(SignatureBase, BaseSchemaWithId):
    """Schema for returning signature data."""
    image_path: str
