"""
Department Pydantic schemas.
"""
from typing import Optional
from pydantic import Field

from app.schemas.base import BaseSchema, BaseSchemaWithId


class DepartmentBase(BaseSchema):
    """Base department schema."""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    code: str = Field(..., min_length=1, max_length=10)
    is_active: bool = True


class DepartmentCreate(DepartmentBase):
    """Schema for creating a department."""
    pass


class DepartmentUpdate(BaseSchema):
    """Schema for updating a department."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    code: Optional[str] = Field(None, min_length=1, max_length=10)
    is_active: Optional[bool] = None


class Department(DepartmentBase, BaseSchemaWithId):
    """Schema for returning department data."""
    pass
