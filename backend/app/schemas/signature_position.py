"""
Signature Position Pydantic schemas.
"""
from typing import Optional
from pydantic import Field

from app.schemas.base import BaseSchema, BaseSchemaWithId


class SignaturePositionBase(BaseSchema):
    """Base signature position schema."""
    x: float = Field(..., ge=0)
    y: float = Field(..., ge=0)
    page_number: int = Field(..., ge=1)
    width: float = Field(..., gt=0)
    height: float = Field(..., gt=0)
    document_id: int
    signature_id: int


class SignaturePositionCreate(SignaturePositionBase):
    """Schema for creating a signature position."""
    auto_detected: bool = False
    confidence_score: Optional[float] = None


class SignaturePositionUpdate(BaseSchema):
    """Schema for updating a signature position."""
    x: Optional[float] = Field(None, ge=0)
    y: Optional[float] = Field(None, ge=0)
    page_number: Optional[int] = Field(None, ge=1)
    width: Optional[float] = Field(None, gt=0)
    height: Optional[float] = Field(None, gt=0)
    is_applied: Optional[bool] = None
    auto_detected: Optional[bool] = None
    confidence_score: Optional[float] = None
    signature_id: Optional[int] = None


class SignaturePosition(SignaturePositionBase, BaseSchemaWithId):
    """Schema for returning signature position data."""
    is_applied: bool
    auto_detected: bool
    confidence_score: Optional[float]
