"""
Document Pydantic schemas.
"""
from typing import Optional
from pydantic import Field

from app.schemas.base import BaseSchema, BaseSchemaWithId
from app.models.document import DocumentStatus


class DocumentBase(BaseSchema):
    """Base document schema."""
    title: Optional[str] = None
    author: Optional[str] = None
    subject: Optional[str] = None
    keywords: Optional[str] = None


class DocumentCreate(DocumentBase):
    """Schema for creating a document."""
    filename: str
    original_filename: str
    file_path: str
    file_size: int
    mime_type: str = "application/pdf"
    uploaded_by_id: int


class DocumentUpdate(BaseSchema):
    """Schema for updating a document."""
    title: Optional[str] = None
    author: Optional[str] = None
    subject: Optional[str] = None
    keywords: Optional[str] = None
    status: Optional[DocumentStatus] = None
    error_message: Optional[str] = None
    page_count: Optional[int] = None
    text_content: Optional[str] = None
    detected_department: Optional[str] = None
    confidence_score: Optional[float] = None
    department_id: Optional[int] = None


class Document(DocumentBase, BaseSchemaWithId):
    """Schema for returning document data."""
    filename: str
    original_filename: str
    file_path: str
    file_size: int
    mime_type: str
    status: DocumentStatus
    error_message: Optional[str]
    page_count: Optional[int]
    text_content: Optional[str]
    detected_department: Optional[str]
    confidence_score: Optional[float]
    uploaded_by_id: int
    department_id: Optional[int]
