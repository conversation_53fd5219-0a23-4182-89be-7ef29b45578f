"""
Base Pydantic schemas with common fields.
"""
from datetime import datetime
from typing import Optional
from pydantic import BaseModel, ConfigDict


class BaseSchema(BaseModel):
    """
    Base schema with common configuration.
    """
    model_config = ConfigDict(from_attributes=True)


class BaseSchemaWithId(BaseSchema):
    """
    Base schema with ID and timestamps.
    """
    id: int
    created_at: datetime
    updated_at: datetime
