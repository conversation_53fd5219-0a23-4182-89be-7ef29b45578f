#!/usr/bin/env python3
"""
🔏 Sistema de Firmado PDF - Aplicación GUI
Interfaz gráfica moderna para Mac y Windows
"""

import sys
import platform
import subprocess
from pathlib import Path

# Verificación temprana de tkinter con manejo de errores específico para macOS
def check_tkinter_early():
    """Verificar tkinter antes de importar otros módulos"""
    try:
        import tkinter as tk
        # Probar creación de ventana
        root = tk.Tk()
        root.withdraw()
        root.destroy()
        return True, None
    except ImportError as e:
        error_msg = str(e)
        if "No module named '_tkinter'" in error_msg:
            return False, "tkinter_missing"
        elif "No module named 'tkinter'" in error_msg:
            return False, "tkinter_not_installed"
        else:
            return False, f"tkinter_error: {error_msg}"
    except Exception as e:
        return False, f"tkinter_runtime_error: {e}"

def show_tkinter_error_gui(error_type):
    """Mostrar error de tkinter usando dialog nativo del sistema"""
    if platform.system() == "Darwin":  # macOS
        title = "Error de tkinter - Sistema de Firmado PDF"

        if error_type == "tkinter_missing":
            message = """tkinter no está disponible en esta instalación de Python.

SOLUCIONES para macOS:

1. AUTOMÁTICA (Recomendada):
   Ejecutar: python3 install_macos.py

2. HOMEBREW:
   brew install python-tk
   brew reinstall python@3.13

3. PYTHON OFICIAL:
   Descargar desde: https://www.python.org/downloads/

4. DIAGNÓSTICO:
   Ejecutar: python3 diagnose_macos.py

¿Deseas abrir la página de descarga de Python?"""
        else:
            message = f"""Error con tkinter: {error_type}

Ejecuta el diagnóstico: python3 diagnose_macos.py"""

        # Usar osascript para mostrar dialog nativo
        try:
            result = subprocess.run([
                'osascript', '-e',
                f'display dialog "{message}" with title "{title}" buttons {{"Cancelar", "Abrir Python.org"}} default button "Abrir Python.org"'
            ], capture_output=True, text=True)

            if "Abrir Python.org" in result.stdout:
                subprocess.run(['open', 'https://www.python.org/downloads/'])

        except:
            # Fallback a print
            print(f"❌ {title}")
            print(message)

    elif platform.system() == "Windows":
        # Para Windows, usar ctypes para mostrar MessageBox
        try:
            import ctypes
            ctypes.windll.user32.MessageBoxW(0,
                "tkinter no está disponible.\n\nInstala Python desde:\nhttps://www.python.org/downloads/",
                "Error - Sistema de Firmado PDF", 1)
        except:
            print("❌ Error: tkinter no disponible")

    else:
        print("❌ Error: tkinter no disponible")

# Verificar tkinter antes de continuar
tkinter_ok, tkinter_error = check_tkinter_early()

if not tkinter_ok:
    print(f"❌ Error de tkinter: {tkinter_error}")
    show_tkinter_error_gui(tkinter_error)
    sys.exit(1)

# Ahora importar tkinter y otros módulos
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import webbrowser
from PIL import Image, ImageTk
import io

# Importar nuestro sistema de procesamiento
try:
    from main import PDFProcessor, FileManager
    BACKEND_AVAILABLE = True
except ImportError as e:
    BACKEND_AVAILABLE = False
    IMPORT_ERROR = str(e)

class PDFSignerApp:
    def __init__(self, root):
        self.root = root
        self.setup_window()
        self.setup_variables()
        self.setup_styles()
        self.create_widgets()
        self.check_dependencies()
        
        # Variables de estado
        self.pdf_path = None
        self.signature_path = None
        self.processor = None
        
    def setup_window(self):
        """Configurar ventana principal"""
        self.root.title("🔏 Sistema de Firmado PDF")
        self.root.geometry("800x700")
        self.root.minsize(600, 500)
        
        # Configurar para diferentes OS
        if platform.system() == "Darwin":  # macOS
            self.root.configure(bg='#f0f0f0')
        elif platform.system() == "Windows":
            self.root.configure(bg='#f0f0f0')
        
        # Centrar ventana
        self.center_window()
        
    def center_window(self):
        """Centrar ventana en pantalla"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
    def setup_variables(self):
        """Configurar variables de tkinter"""
        self.pdf_var = tk.StringVar(value="Ningún archivo seleccionado")
        self.signature_var = tk.StringVar(value="Ninguna firma seleccionada")
        self.offset_var = tk.StringVar(value="0")
        self.verbose_var = tk.BooleanVar(value=True)
        self.status_var = tk.StringVar(value="Listo para procesar")
        
    def setup_styles(self):
        """Configurar estilos modernos"""
        style = ttk.Style()
        
        # Configurar tema según OS
        if platform.system() == "Darwin":
            style.theme_use('aqua')
        elif platform.system() == "Windows":
            style.theme_use('vista')
        else:
            style.theme_use('clam')
            
        # Estilos personalizados
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'))
        style.configure('Subtitle.TLabel', font=('Arial', 12, 'bold'))
        style.configure('Info.TLabel', font=('Arial', 10))
        style.configure('Success.TLabel', foreground='green')
        style.configure('Error.TLabel', foreground='red')
        style.configure('Big.TButton', font=('Arial', 12, 'bold'))
        
    def create_widgets(self):
        """Crear todos los widgets de la interfaz"""
        # Frame principal con scroll
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configurar grid
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        row = 0
        
        # Título
        title_label = ttk.Label(main_frame, text="🔏 Sistema de Firmado PDF", style='Title.TLabel')
        title_label.grid(row=row, column=0, columnspan=3, pady=(0, 20))
        row += 1
        
        # Información del sistema
        info_text = f"Sistema: {platform.system()} {platform.release()}"
        info_label = ttk.Label(main_frame, text=info_text, style='Info.TLabel')
        info_label.grid(row=row, column=0, columnspan=3, pady=(0, 10))
        row += 1
        
        # Sección 1: Selección de archivos
        self.create_file_selection_section(main_frame, row)
        row += 4
        
        # Sección 2: Configuración
        self.create_config_section(main_frame, row)
        row += 3
        
        # Sección 3: Preview
        self.create_preview_section(main_frame, row)
        row += 3
        
        # Sección 4: Procesamiento
        self.create_processing_section(main_frame, row)
        row += 4
        
        # Sección 5: Log
        self.create_log_section(main_frame, row)
        
    def create_file_selection_section(self, parent, start_row):
        """Crear sección de selección de archivos"""
        # Título de sección
        ttk.Label(parent, text="📁 Selección de Archivos", style='Subtitle.TLabel').grid(
            row=start_row, column=0, columnspan=3, sticky=tk.W, pady=(10, 5))
        
        # PDF
        ttk.Label(parent, text="PDF:").grid(row=start_row+1, column=0, sticky=tk.W, padx=(20, 10))
        ttk.Label(parent, textvariable=self.pdf_var, style='Info.TLabel').grid(
            row=start_row+1, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        ttk.Button(parent, text="Seleccionar PDF", command=self.select_pdf).grid(
            row=start_row+1, column=2, sticky=tk.E)
        
        # Firma
        ttk.Label(parent, text="Firma:").grid(row=start_row+2, column=0, sticky=tk.W, padx=(20, 10))
        ttk.Label(parent, textvariable=self.signature_var, style='Info.TLabel').grid(
            row=start_row+2, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        ttk.Button(parent, text="Seleccionar Firma", command=self.select_signature).grid(
            row=start_row+2, column=2, sticky=tk.E)
        
        # Separador
        ttk.Separator(parent, orient='horizontal').grid(
            row=start_row+3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
            
    def create_config_section(self, parent, start_row):
        """Crear sección de configuración"""
        ttk.Label(parent, text="⚙️ Configuración", style='Subtitle.TLabel').grid(
            row=start_row, column=0, columnspan=3, sticky=tk.W, pady=(10, 5))
        
        # Offset Y
        ttk.Label(parent, text="Ajuste vertical (px):").grid(
            row=start_row+1, column=0, sticky=tk.W, padx=(20, 10))
        offset_entry = ttk.Entry(parent, textvariable=self.offset_var, width=10)
        offset_entry.grid(row=start_row+1, column=1, sticky=tk.W)
        
        # Modo verbose
        ttk.Checkbutton(parent, text="Mostrar información detallada", 
                       variable=self.verbose_var).grid(
            row=start_row+2, column=0, columnspan=2, sticky=tk.W, padx=(20, 0), pady=5)
        
    def create_preview_section(self, parent, start_row):
        """Crear sección de preview"""
        ttk.Label(parent, text="🖼️ Preview", style='Subtitle.TLabel').grid(
            row=start_row, column=0, columnspan=3, sticky=tk.W, pady=(10, 5))
        
        # Frame para preview
        self.preview_frame = ttk.Frame(parent, relief='sunken', borderwidth=2)
        self.preview_frame.grid(row=start_row+1, column=0, columnspan=3, 
                               sticky=(tk.W, tk.E), pady=5, ipady=10)
        
        self.preview_label = ttk.Label(self.preview_frame, 
                                      text="Selecciona archivos para ver preview")
        self.preview_label.pack(pady=20)
        
        # Botón de preview
        ttk.Button(parent, text="Generar Preview", command=self.generate_preview).grid(
            row=start_row+2, column=1, pady=5)
            
    def create_processing_section(self, parent, start_row):
        """Crear sección de procesamiento"""
        ttk.Label(parent, text="🚀 Procesamiento", style='Subtitle.TLabel').grid(
            row=start_row, column=0, columnspan=3, sticky=tk.W, pady=(10, 5))
        
        # Botones principales
        button_frame = ttk.Frame(parent)
        button_frame.grid(row=start_row+1, column=0, columnspan=3, pady=10)
        
        self.analyze_btn = ttk.Button(button_frame, text="📖 Analizar PDF", 
                                     command=self.analyze_pdf, style='Big.TButton')
        self.analyze_btn.pack(side=tk.LEFT, padx=5)
        
        self.process_btn = ttk.Button(button_frame, text="🔏 Procesar Todo", 
                                     command=self.process_all, style='Big.TButton')
        self.process_btn.pack(side=tk.LEFT, padx=5)
        
        # Barra de progreso
        self.progress = ttk.Progressbar(parent, mode='indeterminate')
        self.progress.grid(row=start_row+2, column=0, columnspan=3, 
                          sticky=(tk.W, tk.E), pady=5)
        
        # Estado
        self.status_label = ttk.Label(parent, textvariable=self.status_var, style='Info.TLabel')
        self.status_label.grid(row=start_row+3, column=0, columnspan=3, pady=5)
        
    def create_log_section(self, parent, start_row):
        """Crear sección de log"""
        ttk.Label(parent, text="📋 Log de Actividad", style='Subtitle.TLabel').grid(
            row=start_row, column=0, columnspan=3, sticky=tk.W, pady=(10, 5))
        
        # Área de texto con scroll
        self.log_text = scrolledtext.ScrolledText(parent, height=8, width=70)
        self.log_text.grid(row=start_row+1, column=0, columnspan=3, 
                          sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        
        # Configurar expansión
        parent.rowconfigure(start_row+1, weight=1)
        
        # Botones de log
        log_button_frame = ttk.Frame(parent)
        log_button_frame.grid(row=start_row+2, column=0, columnspan=3, pady=5)
        
        ttk.Button(log_button_frame, text="Limpiar Log", 
                  command=self.clear_log).pack(side=tk.LEFT, padx=5)
        ttk.Button(log_button_frame, text="Abrir Carpeta", 
                  command=self.open_output_folder).pack(side=tk.LEFT, padx=5)
        
    def check_dependencies(self):
        """Verificar dependencias del sistema"""
        if not BACKEND_AVAILABLE:
            self.log_message(f"❌ Error: {IMPORT_ERROR}", "error")
            self.log_message("📦 Instala las dependencias: pip install PyMuPDF pdfplumber pillow", "error")
            self.disable_processing()
        else:
            self.log_message("✅ Sistema listo para usar", "success")
            
    def disable_processing(self):
        """Deshabilitar botones de procesamiento"""
        if hasattr(self, 'analyze_btn'):
            self.analyze_btn.configure(state='disabled')
        if hasattr(self, 'process_btn'):
            self.process_btn.configure(state='disabled')
            
    def log_message(self, message, level="info"):
        """Agregar mensaje al log"""
        if hasattr(self, 'log_text'):
            self.log_text.insert(tk.END, f"{message}\n")
            self.log_text.see(tk.END)
            
            # Colorear según nivel
            if level == "error":
                # Configurar tag para errores
                self.log_text.tag_add("error", "end-2l", "end-1l")
                self.log_text.tag_config("error", foreground="red")
            elif level == "success":
                self.log_text.tag_add("success", "end-2l", "end-1l")
                self.log_text.tag_config("success", foreground="green")
                
    def clear_log(self):
        """Limpiar el log"""
        if hasattr(self, 'log_text'):
            self.log_text.delete(1.0, tk.END)
            
    def select_pdf(self):
        """Seleccionar archivo PDF"""
        filename = filedialog.askopenfilename(
            title="Seleccionar PDF con liquidaciones",
            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")]
        )
        if filename:
            self.pdf_path = filename
            self.pdf_var.set(Path(filename).name)
            self.log_message(f"📄 PDF seleccionado: {Path(filename).name}")
            self.validate_files()
            
    def select_signature(self):
        """Seleccionar imagen de firma"""
        filename = filedialog.askopenfilename(
            title="Seleccionar imagen de firma",
            filetypes=[("Image files", "*.png *.jpg *.jpeg"), ("All files", "*.*")]
        )
        if filename:
            self.signature_path = filename
            self.signature_var.set(Path(filename).name)
            self.log_message(f"✍️ Firma seleccionada: {Path(filename).name}")
            self.validate_files()
            
    def validate_files(self):
        """Validar archivos seleccionados"""
        if self.pdf_path and self.signature_path and BACKEND_AVAILABLE:
            # Validar PDF
            pdf_valid, pdf_msg = FileManager.validate_pdf(self.pdf_path)
            if pdf_valid:
                self.log_message(f"✅ {pdf_msg}", "success")
            else:
                self.log_message(f"❌ {pdf_msg}", "error")
                return
                
            # Validar firma
            sig_valid, sig_msg = FileManager.validate_signature(self.signature_path)
            if sig_valid:
                self.signature_path = sig_valid  # Actualizar si se convirtió
                self.log_message(f"✅ {sig_msg}", "success")
                self.status_var.set("Archivos listos - Puedes analizar el PDF")
            else:
                self.log_message(f"❌ {sig_msg}", "error")

    def generate_preview(self):
        """Generar preview de la primera página con firma"""
        if not self.pdf_path or not self.signature_path:
            messagebox.showwarning("Archivos faltantes",
                                 "Selecciona tanto el PDF como la firma")
            return

        if not BACKEND_AVAILABLE:
            messagebox.showerror("Error", "Sistema no disponible")
            return

        def preview_thread():
            try:
                self.progress.start()
                self.status_var.set("Generando preview...")

                # Crear procesador temporal
                offset_y = float(self.offset_var.get()) if self.offset_var.get() else None
                processor = PDFProcessor(self.pdf_path, self.signature_path, False)

                # Analizar solo para preview
                success, message = processor.analyze_pdf()
                if not success:
                    self.log_message(f"❌ Error en análisis: {message}", "error")
                    return

                # Generar preview
                success, message, preview_data = processor.generate_preview(offset_y)
                if success:
                    self.log_message(f"✅ {message}", "success")
                    self.show_preview_image(preview_data)
                else:
                    self.log_message(f"❌ {message}", "error")

            except Exception as e:
                self.log_message(f"❌ Error generando preview: {e}", "error")
            finally:
                self.progress.stop()
                self.status_var.set("Preview completado")

        threading.Thread(target=preview_thread, daemon=True).start()

    def show_preview_image(self, image_data):
        """Mostrar imagen de preview en la interfaz"""
        try:
            # Cargar imagen desde bytes
            image = Image.open(io.BytesIO(image_data))

            # Redimensionar para preview (máximo 400x300)
            image.thumbnail((400, 300), Image.Resampling.LANCZOS)

            # Convertir para tkinter
            photo = ImageTk.PhotoImage(image)

            # Actualizar label de preview
            self.preview_label.configure(image=photo, text="")
            self.preview_label.image = photo  # Mantener referencia

        except Exception as e:
            self.log_message(f"❌ Error mostrando preview: {e}", "error")

    def analyze_pdf(self):
        """Analizar PDF y mostrar estadísticas"""
        if not self.pdf_path or not self.signature_path:
            messagebox.showwarning("Archivos faltantes",
                                 "Selecciona tanto el PDF como la firma")
            return

        if not BACKEND_AVAILABLE:
            messagebox.showerror("Error", "Sistema no disponible")
            return

        def analyze_thread():
            try:
                self.progress.start()
                self.status_var.set("Analizando PDF...")

                # Crear procesador
                self.processor = PDFProcessor(self.pdf_path, self.signature_path,
                                            self.verbose_var.get())

                # Analizar
                success, message = self.processor.analyze_pdf()
                if success:
                    self.log_message(f"✅ {message}", "success")

                    # Mostrar estadísticas
                    stats = self.processor.get_detailed_statistics()
                    self.log_message("📊 Estadísticas del análisis:")
                    self.log_message(f"   📄 Total páginas: {stats['total_pages']}")
                    self.log_message(f"   ✅ Páginas válidas: {stats['valid_pages']}")
                    self.log_message(f"   🏢 Departamentos: {stats['departments_found']}")

                    if stats['departments_found'] > 0:
                        self.log_message("   📂 Distribución por departamento:")
                        for dept, count in stats['department_breakdown'].items():
                            self.log_message(f"      • {dept}: {count} empleados")

                    self.status_var.set("Análisis completado - Listo para procesar")
                else:
                    self.log_message(f"❌ {message}", "error")
                    self.status_var.set("Error en análisis")

            except Exception as e:
                self.log_message(f"❌ Error en análisis: {e}", "error")
                self.status_var.set("Error en análisis")
            finally:
                self.progress.stop()

        threading.Thread(target=analyze_thread, daemon=True).start()

    def process_all(self):
        """Procesar todas las páginas"""
        if not self.processor:
            messagebox.showwarning("Análisis requerido",
                                 "Primero debes analizar el PDF")
            return

        # Confirmar procesamiento
        stats = self.processor.get_detailed_statistics()
        result = messagebox.askyesno(
            "Confirmar procesamiento",
            f"¿Procesar {stats['valid_pages']} páginas en {stats['departments_found']} departamentos?\n\n"
            "Esto creará archivos individuales y un ZIP organizado."
        )

        if not result:
            return

        def process_thread():
            try:
                self.progress.start()
                self.status_var.set("Procesando páginas...")

                # Procesar con offset personalizado
                offset_y = float(self.offset_var.get()) if self.offset_var.get() else None

                success, message = self.processor.process_all(offset_y)
                if success:
                    self.log_message(f"✅ {message}", "success")

                    # Crear ZIP
                    self.status_var.set("Creando archivo ZIP...")
                    zip_path = self.processor.create_zip()
                    self.log_message(f"📦 ZIP creado: {zip_path}", "success")

                    # Mostrar resumen final
                    stats = self.processor.get_detailed_statistics()
                    self.log_message("🎉 ¡PROCESO COMPLETADO!")
                    self.log_message(f"📁 Archivos creados: {stats['valid_pages']}")
                    self.log_message(f"🏢 Departamentos: {stats['departments_found']}")

                    self.status_var.set("Proceso completado exitosamente")

                    # Preguntar si abrir carpeta
                    if messagebox.askyesno("Proceso completado",
                                         "¿Abrir carpeta con los archivos generados?"):
                        self.open_output_folder()

                else:
                    self.log_message(f"❌ {message}", "error")
                    self.status_var.set("Error en procesamiento")

            except Exception as e:
                self.log_message(f"❌ Error en procesamiento: {e}", "error")
                self.status_var.set("Error en procesamiento")
            finally:
                self.progress.stop()

        threading.Thread(target=process_thread, daemon=True).start()

    def open_output_folder(self):
        """Abrir carpeta de salida"""
        output_path = Path("liquidaciones_firmadas")
        if output_path.exists():
            if platform.system() == "Darwin":  # macOS
                import subprocess
                subprocess.run(["open", str(output_path)])
            elif platform.system() == "Windows":
                import subprocess
                subprocess.run(["explorer", str(output_path)])
            else:  # Linux
                import subprocess
                subprocess.run(["xdg-open", str(output_path)])
        else:
            messagebox.showinfo("Carpeta no encontrada",
                              "La carpeta de salida aún no existe")

def main():
    """Función principal de la aplicación"""
    # Configurar para alta resolución en Windows
    if platform.system() == "Windows":
        try:
            from ctypes import windll
            windll.shcore.SetProcessDpiAwareness(1)
        except:
            pass

    # Crear aplicación
    root = tk.Tk()
    app = PDFSignerApp(root)

    # Manejar cierre de ventana
    def on_closing():
        if messagebox.askokcancel("Salir", "¿Cerrar la aplicación?"):
            root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)

    # Iniciar aplicación
    root.mainloop()

if __name__ == "__main__":
    main()
