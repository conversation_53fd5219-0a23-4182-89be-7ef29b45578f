# Sistema de Firmas PDF

Sistema completo para la gestión y aplicación automática de firmas en documentos PDF, desarrollado con FastAPI y React.

## 🏗️ Arquitectura

### Stack Tecnológico

**Backend:**
- Python 3.11+ con FastAPI
- SQLAlchemy + PostgreSQL
- Celery + Redis para procesamiento asíncrono
- PyMuPDF para manipulación de PDFs

**Frontend:**
- React 18 con TypeScript
- Vite como build tool
- TailwindCSS para styling
- React Query para estado del servidor

**DevOps:**
- Docker + Docker Compose
- Nginx como reverse proxy
- GitHub Actions para CI/CD

## 🚀 Inicio Rápido

### Prerrequisitos
- Docker y Docker Compose
- Node.js 18+ (para desarrollo frontend)
- Python 3.11+ (para desarrollo backend)

### Instalación

1. **Clonar el repositorio:**
```bash
git clone <repository-url>
cd pdf-signature-system
```

2. **Levantar servicios con Docker:**
```bash
docker-compose up -d
```

3. **Acceder a la aplicación:**
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- Documentación API: http://localhost:8000/docs

## 📁 Estructura del Proyecto

```
pdf-signature-system/
├── backend/                 # API FastAPI
│   ├── app/
│   │   ├── core/           # Configuración y seguridad
│   │   ├── models/         # Modelos SQLAlchemy
│   │   ├── schemas/        # Esquemas Pydantic
│   │   ├── services/       # Lógica de negocio
│   │   ├── api/            # Endpoints FastAPI
│   │   ├── workers/        # Tareas Celery
│   │   └── utils/          # Utilidades
│   ├── tests/              # Tests unitarios
│   └── alembic/            # Migraciones DB
├── frontend/               # Aplicación React
│   ├── src/
│   │   ├── components/     # Componentes reutilizables
│   │   ├── pages/          # Páginas de la aplicación
│   │   ├── hooks/          # Custom hooks
│   │   ├── services/       # Llamadas a API
│   │   ├── types/          # Tipos TypeScript
│   │   └── utils/          # Utilidades
│   └── public/             # Archivos estáticos
└── docker-compose.yml      # Orquestación de servicios
```

## 🔧 Desarrollo

### Backend
```bash
cd backend
pip install -r requirements.txt
uvicorn app.main:app --reload
```

### Frontend
```bash
cd frontend
npm install
npm run dev
```

## 📚 Funcionalidades

- ✅ Carga y análisis de documentos PDF
- ✅ Extracción automática de texto y metadatos
- ✅ Clasificación por departamentos
- ✅ Gestión de firmas digitales
- ✅ Posicionamiento automático de firmas
- ✅ Procesamiento asíncrono en background
- ✅ API REST completa
- ✅ Interfaz web moderna y responsiva

## 🧪 Testing

```bash
# Backend
cd backend
pytest

# Frontend
cd frontend
npm test
```

## 📄 Licencia

Este proyecto está bajo la licencia MIT.
