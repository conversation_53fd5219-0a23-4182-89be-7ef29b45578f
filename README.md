# 🔏 Sistema de Firmado PDF - Aplicación Local

Sistema automatizado con **interfaz gráfica moderna** para aplicar firmas digitales a documentos PDF de liquidaciones de remuneraciones. Funciona en **Mac y Windows** con instalación automática.

## ✨ Características Principales

- 🖥️ **Interfaz gráfica moderna** - Fácil de usar, sin línea de comandos
- 🍎🪟 **Multiplataforma** - Funciona en Mac y Windows
- ⚡ **Instalación automática** - Un clic para instalar dependencias
- 📱 **Preview en tiempo real** - Ve cómo quedará la firma antes de procesar
- 🏢 **Organización automática** por departamentos
- 👤 **Archivos individuales** por empleado
- 🔍 **Detección inteligente** de nombres y departamentos
- 📦 **Compresión automática** en archivo ZIP
- 🎯 **Posicionamiento inteligente** de firmas
- 📊 **Estadísticas detalladas** y log en tiempo real

## 🚀 Instalación Rápida

### Opción 1: Instalación Automática (Recomendada)

**Windows:**
1. <PERSON>cargar todos los archivos
2. Doble clic en `start_windows.bat`
3. ¡Listo! La aplicación se abrirá automáticamente

**Mac:**
1. Descargar todos los archivos
2. Doble clic en `start_mac.command`
3. ¡Listo! La aplicación se abrirá automáticamente

### Opción 2: Instalación Manual

```bash
# 1. Instalar dependencias automáticamente
python install.py

# 2. Ejecutar aplicación
python app_gui.py
```

### Opción 3: Desde línea de comandos

```bash
# Instalar dependencias
pip install -r requirements.txt

# Ejecutar interfaz gráfica
python app_gui.py

# O usar línea de comandos
python main.py archivo.pdf firma.png
```

## 🖥️ Uso de la Interfaz Gráfica

### Pasos simples:

1. **🚀 Abrir aplicación**
   - Windows: Doble clic en `start_windows.bat`
   - Mac: Doble clic en `start_mac.command`
   - Manual: `python app_gui.py`

2. **📁 Seleccionar archivos**
   - Clic en "Seleccionar PDF" → Elegir archivo con liquidaciones
   - Clic en "Seleccionar Firma" → Elegir imagen de firma (PNG recomendado)

3. **⚙️ Configurar (opcional)**
   - Ajustar posición vertical de firma
   - Activar modo detallado

4. **🖼️ Preview**
   - Clic en "Generar Preview" para ver cómo quedará la firma

5. **📖 Analizar**
   - Clic en "Analizar PDF" para detectar empleados y departamentos

6. **🔏 Procesar**
   - Clic en "Procesar Todo" para generar archivos firmados

### 💻 Uso desde línea de comandos (avanzado)

```bash
# Sintaxis básica
python main.py liquidaciones.pdf firma.png

# Con ajuste de posición
python main.py liquidaciones.pdf firma.png --offset-y 50 --verbose
```

## 📁 Estructura de archivos

### Archivos de entrada requeridos:
- **PDF**: Documento con liquidaciones de remuneraciones
- **Imagen de firma**: PNG, JPG o JPEG (PNG con transparencia recomendado)

### Archivos generados:
```
liquidaciones_firmadas/           # Directorio principal
├── DEPARTAMENTO_EDUCACION/       # Directorio por departamento
│   ├── JUAN_PEREZ_pag_001.pdf   # Archivo individual por empleado
│   ├── MARIA_LOPEZ_pag_002.pdf
│   └── ...
├── ADMINISTRACION_MUNICIPAL/
│   ├── CARLOS_SILVA_pag_003.pdf
│   └── ...
└── ...

liquidaciones_firmadas_organizadas.zip  # Archivo comprimido
preview_firma.png                        # Preview de la primera página
```

## 🔧 Uso programático

También puedes usar el sistema desde tu propio código Python:

```python
from main import PDFProcessor, FileManager

# Validar archivos
pdf_path, status = FileManager.validate_pdf("liquidaciones.pdf")
sig_path, status = FileManager.validate_signature("firma.png")

# Crear procesador
processor = PDFProcessor(pdf_path, sig_path, verbose=True)

# Analizar PDF
success, message = processor.analyze_pdf()

# Procesar todas las páginas
success, message = processor.process_all()

# Crear ZIP
zip_path = processor.create_zip()
```

Ver `ejemplo_uso.py` para un ejemplo completo.

## 📊 Información técnica

### Formatos soportados
- **PDF**: Cualquier PDF con texto extraíble
- **Imágenes**: PNG (recomendado), JPG, JPEG

### Detección automática
El sistema detecta automáticamente:
- Nombres de empleados
- Departamentos/unidades
- Posiciones óptimas para firmas
- Validez de páginas de liquidación

### Palabras clave reconocidas
- "LIQUIDACIÓN DE REMUNERACIONES"
- "RUT", "HABERES", "DESCUENTOS"
- "LÍQUIDO A PAGAR"
- "DEPARTAMENTO", "UNIDAD"
- Y muchas más...

## 🐛 Solución de problemas

### Error: "No se ha podido resolver la importación"
```bash
# Reinstalar dependencias
pip install --upgrade PyMuPDF pdfplumber pillow tqdm
```

### Error: "Archivo no encontrado"
- Verificar que los archivos existen en la ruta especificada
- Usar rutas absolutas si es necesario

### Error: "No se encontraron páginas válidas"
- Verificar que el PDF contiene liquidaciones de remuneraciones
- Revisar que el texto es extraíble (no es imagen escaneada)

### Firma no se posiciona correctamente
- Usar `--offset-y` para ajustar posición vertical
- Verificar que la imagen de firma tiene el tamaño adecuado

## 📦 Crear Ejecutables (Opcional)

Para distribuir la aplicación sin requerir Python:

```bash
# Instalar PyInstaller
pip install pyinstaller

# Crear ejecutable
python build_app.py
```

Esto genera:
- **Windows**: `Sistema_Firmado_PDF_Windows.zip` con `.exe`
- **Mac**: `Sistema_Firmado_PDF_macOS.dmg` con `.app`

## 📝 Notas importantes

1. **Calidad del PDF**: El sistema funciona mejor con PDFs que contienen texto extraíble
2. **Formato de firma**: PNG con transparencia da mejores resultados
3. **Tamaño de firma**: Recomendado 180x70 píxeles aproximadamente
4. **Memoria**: Para PDFs grandes, el procesamiento puede tomar varios minutos
5. **Compatibilidad**: Probado en Windows 10/11 y macOS 10.15+

## 🤝 Soporte

### Problemas comunes:

**"Error importing required libraries"**
- Ejecutar: `python install.py`
- O manual: `pip install -r requirements.txt`

**"No module named '_tkinter'" (macOS)**
- **Solución rápida**: `python3 fix_tkinter.py`
- **Instalador completo**: `python3 install_macos.py`
- **Diagnóstico**: `python3 diagnose_macos.py`
- **Manual**: Instalar Python oficial desde python.org

**"No se encontraron páginas válidas"**
- Verificar que el PDF contiene liquidaciones de remuneraciones
- Asegurar que el texto es extraíble (no imagen escaneada)

**Firma no se posiciona bien**
- Usar el ajuste "Offset Y" en la interfaz
- Probar con diferentes valores (+/- 50 píxeles)

**Aplicación no abre en Mac**
- Ejecutar: `chmod +x start_mac.command`
- Permitir aplicaciones de desarrolladores no identificados

**Python de Homebrew sin tkinter**
- `brew install python-tk`
- `brew reinstall python@3.13`
- O usar Python oficial: https://www.python.org/downloads/

### Archivos del sistema:

- `app_gui.py` - Interfaz gráfica principal
- `main.py` - Motor de procesamiento
- `install.py` - Instalador automático
- `build_app.py` - Constructor de ejecutables
- `start_windows.bat` - Iniciador Windows
- `start_mac.command` - Iniciador Mac

## 📄 Licencia

Este software es de uso libre para fines educativos y administrativos.
